#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
电子病历录制服务
支持多路并发WebRTC流录制，基于SRS HTTP-FLV流
"""

import os
import sys
import json
import time
import threading
import subprocess
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, List
from dataclasses import dataclass, asdict
from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
import yaml

# 导入配置加载器
from config_loader import ConfigLoader, get_config

# 禁用代理设置
os.environ['NO_PROXY'] = '*'
os.environ['no_proxy'] = '*'
os.environ['HTTP_PROXY'] = ''
os.environ['HTTPS_PROXY'] = ''
os.environ['http_proxy'] = ''
os.environ['https_proxy'] = ''

# 创建无代理的requests session
no_proxy_session = requests.Session()
no_proxy_session.proxies = {
    'http': None,
    'https': None,
}
no_proxy_session.trust_env = False  # 不信任环境变量中的代理设置

# 初始化配置
config = get_config()

# 配置日志
log_config = config.get_logging_config()
file_paths = config.get_file_paths()

# 确保日志目录存在
Path(file_paths['log_dir']).mkdir(parents=True, exist_ok=True)

logging.basicConfig(
    level=getattr(logging, log_config.get('level', 'INFO')),
    format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
    handlers=[
        logging.FileHandler(os.path.join(file_paths['log_dir'], 'recording_service.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class RecordingSession:
    """录制会话信息"""
    task_id: str
    stream_key: str
    start_time: datetime
    status: str  # 完整生命周期状态
    process: Optional[subprocess.Popen] = None
    output_file: Optional[str] = None
    temp_file: Optional[str] = None
    error_message: Optional[str] = None

    # 医疗信息字段
    userid: Optional[str] = None
    username: Optional[str] = None
    chief_complaint: Optional[str] = None
    present_illness: Optional[str] = None
    past_medical_history: Optional[str] = None
    allergic_history: Optional[str] = None

    # 扩展字段，支持未来添加更多信息
    metadata: Optional[Dict] = None

    # 新增生命周期管理字段
    end_time: Optional[datetime] = None
    duration: Optional[str] = None
    file_size: Optional[int] = None
    format_type: str = 'video'  # 'video' 或 'audio'

    # 生命周期状态常量
    STATUS_RECORDING_STARTED = 'recording_started'
    STATUS_RECORDING_IN_PROGRESS = 'recording_in_progress'
    STATUS_RECORDING_STOPPED = 'recording_stopped'
    STATUS_SPEECH_RECOGNITION = 'speech_recognition'
    STATUS_MEDICAL_RECORD_GENERATION = 'medical_record_generation'
    STATUS_COMPLETED = 'completed'
    STATUS_ERROR = 'error'

    def to_dict(self):
        """转换为字典格式，用于JSON序列化"""
        data = asdict(self)
        data['start_time'] = self.start_time.isoformat() if self.start_time else None
        data['process'] = self.process.pid if self.process else None
        return data

class RecordingManager:
    """录制管理器"""

    def __init__(self, config_loader: ConfigLoader):
        self.config_loader = config_loader
        self.server_config = config_loader.get_recording_server_config()
        self.file_paths = config_loader.get_file_paths()
        self.ffmpeg_config = config_loader.get_ffmpeg_config()
        self.sessions: Dict[str, RecordingSession] = {}  # key: task_id
        self.lock = threading.Lock()

        # 创建必要的目录
        self._create_directories()
        
    def _create_directories(self):
        """创建录制相关目录"""
        dirs = [
            self.file_paths['recording_dir'],
            self.file_paths['temp_dir'],
            self.file_paths['log_dir']
        ]

        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            logger.info(f"确保目录存在: {dir_path}")
    
    def _generate_filename(self, stream_key: str, format_type: str = 'video') -> tuple:
        """生成录制文件名

        Args:
            stream_key: 推流码
            format_type: 录制格式类型 ('video' 或 'audio')
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 根据格式类型确定文件扩展名和目录
        if format_type == 'audio':
            file_ext = 'wav'
            format_dir = 'audio'
        else:
            file_ext = 'mp4'
            format_dir = 'video'

        # 最终文件路径
        final_dir = Path(self.file_paths['recording_dir']) / format_dir / stream_key
        final_dir.mkdir(parents=True, exist_ok=True)
        final_file = final_dir / f"{timestamp}.{file_ext}"

        # 临时文件路径
        temp_dir = Path(self.file_paths['temp_dir'])
        temp_file = temp_dir / f"{stream_key}_{timestamp}_temp.{file_ext}"

        return str(final_file), str(temp_file)
    
    def _check_stream_availability(self, stream_key: str) -> bool:
        """检查流是否可用"""
        # 使用SRS API检查流状态
        api_url = f"http://{self.server_config['srs_host']}:{self.server_config['srs_api_port']}/api/v1/streams/"

        try:
            # 使用全局无代理session
            response = no_proxy_session.get(api_url, timeout=5)

            if response.status_code != 200:
                logger.warning(f"SRS API请求失败: {response.status_code}")
                return False

            data = response.json()

            if data.get('code') != 0:
                logger.warning(f"SRS API返回错误: {data}")
                return False

            streams = data.get('streams', [])

            # 查找匹配的流
            for stream in streams:
                if stream.get('name') == stream_key:
                    # 检查流是否活跃
                    publish_info = stream.get('publish', {})
                    is_active = publish_info.get('active', False)

                    logger.info(f"流 {stream_key} 可用性检查: {is_active} (SRS API)")
                    return is_active

            logger.info(f"流 {stream_key} 不存在 (SRS API)")
            return False

        except Exception as e:
            logger.warning(f"流 {stream_key} 可用性检查失败: {e} (SRS API: {api_url})")
            return False
    
    def _test_input_source(self, url: str) -> bool:
        """测试输入源是否可用"""
        try:
            # 对于HTTP-FLV，使用GET请求而不是HEAD，因为某些服务器不支持HEAD
            response = no_proxy_session.get(url, timeout=3, stream=True)
            # 立即关闭连接，我们只是测试可用性
            response.close()
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"输入源测试失败 {url}: {e}")
            return False

    def _build_ffmpeg_command(self, stream_key: str, output_file: str, format_type: str = 'video') -> List[str]:
        """构建FFmpeg录制命令

        Args:
            stream_key: 推流码
            output_file: 输出文件路径
            format_type: 录制格式类型 ('video' 或 'audio')
        """
        # 定义可能的输入源
        input_sources = [
            f"http://{self.server_config['srs_host']}:{self.server_config['srs_http_port']}/live/{stream_key}.flv",
            f"rtmp://{self.server_config['srs_host']}:1935/live/{stream_key}"
        ]

        # 选择可用的输入源
        input_url = None
        for url in input_sources:
            if url.startswith('http'):
                # 对HTTP源进行可用性测试
                if self._test_input_source(url):
                    input_url = url
                    logger.info(f"使用HTTP-FLV输入源: {url}")
                    break
                else:
                    logger.warning(f"HTTP-FLV源不可用: {url}")
            else:
                # RTMP源作为备选（不进行预测试，因为RTMP测试比较复杂）
                input_url = url
                logger.info(f"使用RTMP输入源: {url}")
                break

        if not input_url:
            raise Exception("没有可用的输入源")

        # 根据录制格式构建不同的FFmpeg命令
        if format_type == 'audio':
            # 音频录制：提取音频流并保存为高质量WAV格式
            cmd = [
                'ffmpeg',
                '-i', input_url,                             # 输入流地址
                '-vn',                                       # 禁用视频
                '-acodec', 'pcm_s16le',                     # 音频编码器：16位PCM
                '-ar', '44100',                             # 采样率：44.1kHz
                '-ac', '2',                                 # 声道数：立体声
                '-f', 'wav',                                # 输出格式：WAV
                '-y',                                       # 覆盖输出文件
            ]
        else:
            # 视频录制：直接复制流
            cmd = [
                'ffmpeg',
                '-i', input_url,                                 # 输入流地址
                '-c', 'copy',                                    # 直接复制流，不重新编码（更快，质量更好）
                '-f', self.ffmpeg_config.get('format', 'mp4'),  # 输出格式
                '-y',                                            # 覆盖输出文件
            ]

        # 添加额外参数（仅对视频录制）
        if format_type == 'video':
            extra_args = self.ffmpeg_config.get('extra_args', [])
            if extra_args:
                cmd.extend(extra_args)

        cmd.append(output_file)

        logger.info(f"FFmpeg {format_type} 录制命令: {' '.join(cmd)}")
        return cmd
    
    def start_recording(self, task_data: dict, format_type: str = 'video') -> dict:
        """开始录制

        Args:
            task_data: 任务数据
            format_type: 录制格式类型 ('video' 或 'audio')
        """
        # 提取必需字段
        task_id = task_data.get('task_id')
        stream_key = task_data.get('stream_key')

        if not task_id:
            return {
                'code': 1,
                'message': '缺少必需参数: task_id',
                'timestamp': datetime.now().isoformat()
            }

        if not stream_key:
            return {
                'code': 1,
                'message': '缺少必需参数: stream_key',
                'timestamp': datetime.now().isoformat()
            }

        with self.lock:
            # 检查task_id是否已存在
            if task_id in self.sessions:
                session = self.sessions[task_id]
                if session.status in ['starting', 'recording']:
                    return {
                        'code': 1,
                        'message': f'任务 {task_id} 已在录制中',
                        'data': {
                            'task_id': task_id,
                            'recording_status': session.status
                        },
                        'timestamp': datetime.now().isoformat()
                    }

            # 检查stream_key是否被其他任务使用
            for existing_task_id, existing_session in self.sessions.items():
                if (existing_session.stream_key == stream_key and
                    existing_session.status in ['starting', 'recording']):
                    return {
                        'code': 1,
                        'message': f'流 {stream_key} 已被任务 {existing_task_id} 使用',
                        'timestamp': datetime.now().isoformat()
                    }

            # 检查流是否可用
            if not self._check_stream_availability(stream_key):
                return {
                    'code': 1,
                    'message': f'流 {stream_key} 不可用，请确认推流已开始',
                    'timestamp': datetime.now().isoformat()
                }

            # 生成文件路径
            final_file, temp_file = self._generate_filename(stream_key, format_type)

            # 创建录制会话
            session = RecordingSession(
                task_id=task_id,
                stream_key=stream_key,
                start_time=datetime.now(),
                status=RecordingSession.STATUS_RECORDING_STARTED,
                output_file=final_file,
                temp_file=temp_file,
                format_type=format_type,
                userid=task_data.get('userid'),
                username=task_data.get('username'),
                chief_complaint=task_data.get('chief_complaint'),
                present_illness=task_data.get('present_illness'),
                past_medical_history=task_data.get('past_medical_history'),
                allergic_history=task_data.get('allergic_history'),
                metadata=task_data.get('metadata', {})
            )

            try:
                # 构建FFmpeg命令
                cmd = self._build_ffmpeg_command(stream_key, temp_file, format_type)

                # 启动FFmpeg进程
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )

                session.process = process
                session.status = RecordingSession.STATUS_RECORDING_IN_PROGRESS
                self.sessions[task_id] = session

                # 启动监控线程
                monitor_thread = threading.Thread(
                    target=self._monitor_recording,
                    args=(task_id,),
                    daemon=True
                )
                monitor_thread.start()

                logger.info(f"开始录制任务 {task_id}, 流 {stream_key}, PID: {process.pid}")

                return {
                    'code': 0,
                    'message': '录制任务创建成功',
                    'data': {
                        'task_id': task_id,
                        'session_id': f"sess_{int(datetime.now().timestamp())}_{task_id[-3:]}",
                        'webrtc_url': f"webrtc://{self.server_config['srs_host']}:{self.server_config['srs_webrtc_port']}/live/{stream_key}",
                        'rtmp_url': f"rtmp://{self.server_config['srs_host']}:1935/live/{stream_key}",
                        'http_flv_url': f"http://{self.server_config['srs_host']}:{self.server_config['srs_http_port']}/live/{stream_key}.flv",
                        'recording_status': 'started',
                        'created_at': session.start_time.isoformat()
                    },
                    'timestamp': datetime.now().isoformat()
                }

            except Exception as e:
                session.status = 'error'
                session.error_message = str(e)
                logger.error(f"启动录制失败 {task_id}: {e}")

                return {
                    'code': 1,
                    'message': f'启动录制失败: {e}',
                    'timestamp': datetime.now().isoformat()
                }
    
    def stop_recording(self, task_id: str) -> dict:
        """停止录制"""
        if not task_id:
            return {
                'code': 1,
                'message': '缺少必需参数: task_id',
                'timestamp': datetime.now().isoformat()
            }

        with self.lock:
            if task_id not in self.sessions:
                return {
                    'code': 1,
                    'message': f'任务 {task_id} 未在录制中',
                    'timestamp': datetime.now().isoformat()
                }

            session = self.sessions[task_id]

            if session.status not in [RecordingSession.STATUS_RECORDING_IN_PROGRESS, RecordingSession.STATUS_RECORDING_STARTED]:
                return {
                    'code': 1,
                    'message': f'任务 {task_id} 当前状态不允许停止: {session.status}',
                    'timestamp': datetime.now().isoformat()
                }

            try:
                session.status = 'stopping'

                # 终止FFmpeg进程
                if session.process:
                    session.process.terminate()

                    # 等待进程结束
                    try:
                        session.process.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        logger.warning(f"FFmpeg进程 {session.process.pid} 未在10秒内结束，强制终止")
                        session.process.kill()
                        session.process.wait()

                # 移动临时文件到最终位置
                if session.temp_file and os.path.exists(session.temp_file):
                    if session.output_file:
                        os.rename(session.temp_file, session.output_file)
                        logger.info(f"录制文件已保存: {session.output_file}")

                # 更新会话状态和生命周期信息
                session.status = RecordingSession.STATUS_RECORDING_STOPPED
                session.end_time = datetime.now()

                logger.info(f"停止录制任务 {task_id}")

                # 计算录制时长
                duration_seconds = (session.end_time - session.start_time).total_seconds()
                duration_str = f"{int(duration_seconds//3600):02d}:{int((duration_seconds%3600)//60):02d}:{int(duration_seconds%60):02d}"
                session.duration = duration_str

                # 获取文件大小
                file_size = "0MB"
                if session.output_file and os.path.exists(session.output_file):
                    size_bytes = os.path.getsize(session.output_file)
                    session.file_size = size_bytes
                    file_size = f"{size_bytes / (1024*1024):.1f}MB"

                # 自动触发语音转文字处理（异步）
                if session.format_type == 'audio' and session.output_file:
                    self._trigger_speech_recognition(task_id, session.output_file)

                return {
                    'code': 0,
                    'message': '录制任务停止成功',
                    'data': {
                        'task_id': task_id,
                        'session_id': f"sess_{int(session.start_time.timestamp())}_{task_id[-3:]}",
                        'recording_status': 'stopped',
                        'stopped_at': datetime.now().isoformat(),
                        'duration': duration_str,
                        'file_size': file_size,
                        'output_file': session.output_file,
                        'processing_status': {
                            'ffmpeg_recording': 'completed',
                            'funasr_processing': 'pending',
                            'video_clips_upload': 'pending',
                            'dify_analysis': 'pending',
                            'database_save': 'pending'
                        }
                    },
                    'timestamp': datetime.now().isoformat()
                }

            except Exception as e:
                session.status = 'error'
                session.error_message = str(e)
                logger.error(f"停止录制失败 {task_id}: {e}")

                return {
                    'code': 1,
                    'message': f'停止录制失败: {e}',
                    'timestamp': datetime.now().isoformat()
                }

    def _trigger_speech_recognition(self, task_id: str, audio_file: str):
        """触发语音转文字处理（异步）"""
        def speech_recognition_worker():
            try:
                logger.info(f"开始语音转文字处理: {task_id}, 文件: {audio_file}")

                # 更新任务状态
                if task_id in self.sessions:
                    session = self.sessions[task_id]
                    session.lifecycle_status = 'speech_recognition'

                # 调用FunASR API进行语音转文字
                result = self._call_funasr_api(audio_file)

                if result['success']:
                    logger.info(f"语音转文字完成: {task_id}")
                    # 更新处理状态
                    if task_id in self.sessions:
                        session = self.sessions[task_id]
                        session.lifecycle_status = 'medical_record_generation'
                        # 可以在这里继续触发医疗记录生成等后续步骤
                else:
                    logger.error(f"语音转文字失败: {task_id}, 错误: {result['error']}")
                    if task_id in self.sessions:
                        session = self.sessions[task_id]
                        session.lifecycle_status = 'error'
                        session.error_message = f"语音转文字失败: {result['error']}"

            except Exception as e:
                logger.error(f"语音转文字处理异常: {task_id}, 错误: {e}")
                if task_id in self.sessions:
                    session = self.sessions[task_id]
                    session.lifecycle_status = 'error'
                    session.error_message = f"语音转文字处理异常: {e}"

        # 在后台线程中执行语音转文字
        import threading
        thread = threading.Thread(target=speech_recognition_worker)
        thread.daemon = True
        thread.start()

    def _call_funasr_api(self, audio_file: str) -> dict:
        """调用FunASR API进行语音转文字"""
        try:
            import requests

            # FunASR API配置
            funasr_url = "http://127.0.0.1:5412/api/transcribe"

            # 准备请求数据
            with open(audio_file, 'rb') as f:
                files = {'audio': f}
                data = {
                    'language': 'zh-cn',
                    'format': 'wav'
                }

                # 发送请求到FunASR API
                response = requests.post(funasr_url, files=files, data=data, timeout=300)

                if response.status_code == 200:
                    result = response.json()
                    return {
                        'success': True,
                        'transcription': result.get('transcription', ''),
                        'segments': result.get('segments', [])
                    }
                else:
                    return {
                        'success': False,
                        'error': f"FunASR API返回错误: {response.status_code}"
                    }

        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "无法连接到FunASR服务，请确认服务已启动"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"调用FunASR API异常: {e}"
            }
    
    def _monitor_recording(self, task_id: str):
        """监控录制进程"""
        session = self.sessions.get(task_id)
        if not session or not session.process:
            return

        try:
            # 等待进程结束
            _, stderr = session.process.communicate()
            return_code = session.process.returncode

            with self.lock:
                if session.status in ['stopping', RecordingSession.STATUS_RECORDING_STOPPED]:
                    # 正常停止或已经处理过，不需要处理
                    return

                if return_code == 0:
                    logger.info(f"录制进程 {task_id} 正常结束")
                    session.status = 'stopped'
                elif return_code == 255 or return_code == -15:  # SIGTERM终止是正常的
                    logger.info(f"录制进程 {task_id} 被信号终止（正常停止），返回码: {return_code}")
                    session.status = 'stopped'
                else:
                    logger.error(f"录制进程 {task_id} 异常结束，返回码: {return_code}")
                    logger.error(f"FFmpeg错误输出: {stderr}")
                    session.status = 'error'
                    session.error_message = f"FFmpeg进程异常结束: {stderr}"

                # 清理临时文件
                if session.temp_file and os.path.exists(session.temp_file):
                    if session.status == 'stopped' and session.output_file:
                        os.rename(session.temp_file, session.output_file)
                        logger.info(f"录制文件已保存: {session.output_file}")
                    else:
                        os.remove(session.temp_file)
                        logger.info(f"清理临时文件: {session.temp_file}")

        except Exception as e:
            logger.error(f"监控录制进程异常 {task_id}: {e}")
            with self.lock:
                session.status = 'error'
                session.error_message = str(e)
    
    def get_session_status(self, task_id: str) -> Optional[dict]:
        """获取录制会话状态"""
        session = self.sessions.get(task_id)
        return session.to_dict() if session else None

    def get_task_status(self, task_id: str) -> dict:
        """获取任务完整状态信息"""
        session = self.sessions.get(task_id)

        if not session:
            return {
                'code': 1,
                'message': f'任务 {task_id} 不存在',
                'timestamp': datetime.now().isoformat()
            }

        # 计算录制时长
        duration_str = "00:00:00"
        if session.start_time:
            end_time = session.end_time or datetime.now()
            duration_seconds = (end_time - session.start_time).total_seconds()
            duration_str = f"{int(duration_seconds//3600):02d}:{int((duration_seconds%3600)//60):02d}:{int(duration_seconds%60):02d}"

        # 获取文件信息
        file_info = {}
        if session.output_file and os.path.exists(session.output_file):
            file_size = os.path.getsize(session.output_file)
            file_info = {
                'file_path': session.output_file,
                'file_size': file_size,
                'file_size_mb': f"{file_size / (1024*1024):.2f}MB"
            }

        # 根据当前状态确定生命周期阶段
        lifecycle_status = self._determine_lifecycle_status(session)

        return {
            'code': 0,
            'message': '获取任务状态成功',
            'data': {
                'task_id': task_id,
                'stream_key': session.stream_key,
                'format_type': session.format_type,
                'lifecycle_status': lifecycle_status,
                'recording_status': session.status,
                'start_time': session.start_time.isoformat() if session.start_time else None,
                'end_time': session.end_time.isoformat() if session.end_time else None,
                'duration': duration_str,
                'file_info': file_info,
                'medical_info': {
                    'userid': session.userid,
                    'username': session.username,
                    'chief_complaint': session.chief_complaint,
                    'present_illness': session.present_illness,
                    'past_medical_history': session.past_medical_history,
                    'allergic_history': session.allergic_history
                },
                'error_message': session.error_message,
                'metadata': session.metadata or {}
            },
            'timestamp': datetime.now().isoformat()
        }

    def _determine_lifecycle_status(self, session: RecordingSession) -> str:
        """确定任务生命周期状态"""
        if session.status == 'error':
            return RecordingSession.STATUS_ERROR
        elif session.status in ['starting', 'recording']:
            if session.status == 'starting':
                return RecordingSession.STATUS_RECORDING_STARTED
            else:
                return RecordingSession.STATUS_RECORDING_IN_PROGRESS
        elif session.status in ['stopping', 'stopped']:
            # 录制已完成，根据后续处理状态确定
            if session.output_file and os.path.exists(session.output_file):
                # 这里可以根据实际的后续处理状态来确定
                # 目前简化为录制停止状态
                return RecordingSession.STATUS_RECORDING_STOPPED
            else:
                return RecordingSession.STATUS_RECORDING_STOPPED
        else:
            return session.status

    def get_all_tasks_status(self) -> dict:
        """获取所有任务状态"""
        tasks = []
        for task_id, session in self.sessions.items():
            task_status = self.get_task_status(task_id)
            if task_status['code'] == 0:
                tasks.append(task_status['data'])

        return {
            'code': 0,
            'message': '获取所有任务状态成功',
            'data': {
                'total_tasks': len(tasks),
                'tasks': tasks
            },
            'timestamp': datetime.now().isoformat()
        }
    

    


# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 创建录制管理器
recording_manager = RecordingManager(config)

# 新的API端点 - 符合医疗系统规范

# 视频录制接口
@app.route('/api/recording/video/start', methods=['POST'])
def start_video_recording():
    """开始视频录制API - MP4格式"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'code': 1,
                'message': '请求体不能为空',
                'timestamp': datetime.now().isoformat()
            }), 400

        result = recording_manager.start_recording(data, format_type='video')
        status_code = 200 if result.get('code') == 0 else 400

        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"开始视频录制API异常: {e}")
        return jsonify({
            'code': 1,
            'message': f'服务器错误: {e}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/recording/video/stop', methods=['POST'])
def stop_video_recording():
    """停止视频录制API - MP4格式"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'code': 1,
                'message': '请求体不能为空',
                'timestamp': datetime.now().isoformat()
            }), 400

        task_id = data.get('task_id')
        result = recording_manager.stop_recording(task_id)
        status_code = 200 if result.get('code') == 0 else 400

        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"停止视频录制API异常: {e}")
        return jsonify({
            'code': 1,
            'message': f'服务器错误: {e}',
            'timestamp': datetime.now().isoformat()
        }), 500

# 音频录制接口
@app.route('/api/recording/audio/start', methods=['POST'])
def start_audio_recording():
    """开始音频录制API - WAV格式"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'code': 1,
                'message': '请求体不能为空',
                'timestamp': datetime.now().isoformat()
            }), 400

        result = recording_manager.start_recording(data, format_type='audio')
        status_code = 200 if result.get('code') == 0 else 400

        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"开始音频录制API异常: {e}")
        return jsonify({
            'code': 1,
            'message': f'服务器错误: {e}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/recording/audio/stop', methods=['POST'])
def stop_audio_recording():
    """停止音频录制API - WAV格式"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'code': 1,
                'message': '请求体不能为空',
                'timestamp': datetime.now().isoformat()
            }), 400

        task_id = data.get('task_id')
        result = recording_manager.stop_recording(task_id)
        status_code = 200 if result.get('code') == 0 else 400

        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"停止音频录制API异常: {e}")
        return jsonify({
            'code': 1,
            'message': f'服务器错误: {e}',
            'timestamp': datetime.now().isoformat()
        }), 500


# 状态查询接口
@app.route('/api/task/status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """获取任务状态API"""
    try:
        result = recording_manager.get_task_status(task_id)
        status_code = 200 if result.get('code') == 0 else 404
        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"获取任务状态API异常: {e}")
        return jsonify({
            'code': 1,
            'message': f'服务器错误: {e}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/tasks/status', methods=['GET'])
def get_all_tasks_status():
    """获取所有任务状态API"""
    try:
        result = recording_manager.get_all_tasks_status()
        return jsonify(result), 200

    except Exception as e:
        logger.error(f"获取所有任务状态API异常: {e}")
        return jsonify({
            'code': 1,
            'message': f'服务器错误: {e}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查API"""
    return jsonify({
        'success': True,
        'message': '录制服务运行正常',
        'timestamp': datetime.now().isoformat(),
        'active_sessions': len(recording_manager.sessions)
    })

if __name__ == '__main__':
    # 验证配置
    if not config.validate_config():
        logger.error("配置验证失败，服务启动中止")
        sys.exit(1)

    # 获取服务器配置
    server_config = config.get_recording_server_config()

    logger.info("启动录制服务...")
    logger.info(f"配置文件: {config.config_file}")
    logger.info(f"服务器配置: {server_config}")

    # 启动Flask服务器
    app.run(
        host=server_config['server_host'],
        port=server_config['server_port'],
        debug=False,
        threaded=True
    )
