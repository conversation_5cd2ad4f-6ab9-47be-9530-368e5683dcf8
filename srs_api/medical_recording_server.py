#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
电子病历生成系统 - SRS API回调服务器
注意：此服务已被弃用，录制后的语音转文字功能已集成到录制服务中
原功能：接收SRS的录制完成回调，为后续步骤4（funasr处理）做准备
当前状态：已停用，保留代码仅供参考
"""

# 此文件已被弃用，不再使用
# 录制完成后的自动语音转文字功能已直接集成到录制服务中
# 如需重新启用，请参考服务管理操作指南

"""
import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

# 以下代码已被注释，因为SRS API回调服务已被弃用
"""
import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('medical_recording.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置
class Config:
    # SRS录制文件目录
    SRS_RECORDING_DIR = "./srs/trunk/objs/nginx/html/srs_recordings"
    SRS_MEDICAL_DIR = "./srs/trunk/objs/nginx/html/medical_recordings"
    
    # 日志目录
    LOG_DIR = "./logs"
    
    # 回调日志文件
    CALLBACK_LOG_FILE = "./logs/srs_callbacks.log"

# 确保目录存在
os.makedirs(Config.LOG_DIR, exist_ok=True)
os.makedirs(Config.SRS_RECORDING_DIR, exist_ok=True)
os.makedirs(Config.SRS_MEDICAL_DIR, exist_ok=True)

# 存储活跃的录制会话
active_sessions = {}

@app.route('/', methods=['GET'])
def index():
    """首页 - 显示服务状态"""
    return jsonify({
        "service": "电子病历生成系统 - 录制服务器",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "active_sessions": len(active_sessions),
        "endpoints": {
            "srs_callbacks": "/api/hooks/",
            "medical_callbacks": "/api/hooks/medical/",
            "session_status": "/api/session/status",
            "health_check": "/api/health"
        }
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "active_sessions": len(active_sessions)
    })

# ==================== SRS回调接口 ====================

@app.route('/api/hooks/on_publish', methods=['POST'])
def on_publish():
    """推流开始回调 - 步骤1完成"""
    try:
        data = request.get_json() or {}
        logger.info(f"推流开始回调: {data}")
        
        # 提取关键信息
        stream = data.get('stream', 'unknown')
        client_id = data.get('client_id', 'unknown')
        ip = data.get('ip', 'unknown')
        app = data.get('app', 'unknown')
        
        # 记录会话开始
        session_info = {
            "task_id": stream,
            "client_id": client_id,
            "ip": ip,
            "app": app,
            "start_time": datetime.now().isoformat(),
            "status": "publishing",
            "callbacks": ["on_publish"]
        }
        
        active_sessions[stream] = session_info
        
        # 记录到回调日志
        log_callback("on_publish", data)
        
        logger.info(f"推流会话开始 - 任务ID: {stream}, 客户端: {client_id}")
        
        return jsonify({"code": 0, "message": "推流开始回调处理成功"})
        
    except Exception as e:
        logger.error(f"推流开始回调处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"处理失败: {str(e)}"}), 500

@app.route('/api/hooks/on_unpublish', methods=['POST'])
def on_unpublish():
    """推流结束回调 - 步骤3触发"""
    try:
        data = request.get_json() or {}
        logger.info(f"推流结束回调: {data}")
        
        stream = data.get('stream', 'unknown')
        
        # 更新会话状态
        if stream in active_sessions:
            active_sessions[stream]["status"] = "unpublished"
            active_sessions[stream]["end_time"] = datetime.now().isoformat()
            active_sessions[stream]["callbacks"].append("on_unpublish")
        
        # 记录到回调日志
        log_callback("on_unpublish", data)
        
        logger.info(f"推流会话结束 - 任务ID: {stream}")
        
        return jsonify({"code": 0, "message": "推流结束回调处理成功"})
        
    except Exception as e:
        logger.error(f"推流结束回调处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"处理失败: {str(e)}"}), 500

@app.route('/api/hooks/on_dvr', methods=['POST'])
def on_dvr():
    """录制完成回调 - 关键的步骤3，触发后续步骤4"""
    try:
        data = request.get_json() or {}
        logger.info(f"录制完成回调: {data}")
        
        # 提取关键信息
        stream = data.get('stream', 'unknown')
        file_path = data.get('file', 'unknown')
        action = data.get('action', 'unknown')
        
        # 更新会话状态
        if stream in active_sessions:
            active_sessions[stream]["status"] = "recorded"
            active_sessions[stream]["dvr_file"] = file_path
            active_sessions[stream]["dvr_time"] = datetime.now().isoformat()
            active_sessions[stream]["callbacks"].append("on_dvr")
        else:
            # 创建新的会话记录（如果之前没有记录）
            active_sessions[stream] = {
                "task_id": stream,
                "status": "recorded",
                "dvr_file": file_path,
                "dvr_time": datetime.now().isoformat(),
                "callbacks": ["on_dvr"]
            }
        
        # 记录到回调日志
        log_callback("on_dvr", data)
        
        logger.info(f"录制完成 - 任务ID: {stream}, 文件: {file_path}")
        logger.info(f"准备触发步骤4: funasr离线语音处理")
        
        # TODO: 这里将在后续实现中触发步骤4的funasr处理
        # process_recorded_video.delay(stream, file_path)
        
        return jsonify({"code": 0, "message": "录制完成回调处理成功"})
        
    except Exception as e:
        logger.error(f"录制完成回调处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"处理失败: {str(e)}"}), 500

@app.route('/api/hooks/on_play', methods=['POST'])
def on_play():
    """播放开始回调"""
    try:
        data = request.get_json() or {}
        log_callback("on_play", data)
        return jsonify({"code": 0})
    except Exception as e:
        logger.error(f"播放开始回调处理失败: {str(e)}")
        return jsonify({"code": -1, "message": str(e)}), 500

@app.route('/api/hooks/on_stop', methods=['POST'])
def on_stop():
    """播放结束回调"""
    try:
        data = request.get_json() or {}
        log_callback("on_stop", data)
        return jsonify({"code": 0})
    except Exception as e:
        logger.error(f"播放结束回调处理失败: {str(e)}")
        return jsonify({"code": -1, "message": str(e)}), 500

# ==================== 医疗专用回调接口 ====================

@app.route('/api/hooks/medical/on_publish', methods=['POST'])
def medical_on_publish():
    """医疗录制推流开始回调"""
    return on_publish()

@app.route('/api/hooks/medical/on_unpublish', methods=['POST'])
def medical_on_unpublish():
    """医疗录制推流结束回调"""
    return on_unpublish()

@app.route('/api/hooks/medical/on_dvr', methods=['POST'])
def medical_on_dvr():
    """医疗录制完成回调"""
    return on_dvr()

# ==================== 会话管理接口 ====================

@app.route('/api/session/status', methods=['GET'])
def session_status():
    """获取所有会话状态"""
    return jsonify({
        "active_sessions": active_sessions,
        "total_sessions": len(active_sessions),
        "timestamp": datetime.now().isoformat()
    })

@app.route('/api/session/status/<task_id>', methods=['GET'])
def get_session_status(task_id):
    """获取特定会话状态"""
    if task_id in active_sessions:
        return jsonify({
            "task_id": task_id,
            "session": active_sessions[task_id],
            "timestamp": datetime.now().isoformat()
        })
    else:
        return jsonify({
            "task_id": task_id,
            "error": "会话不存在",
            "timestamp": datetime.now().isoformat()
        }), 404

# ==================== 工具函数 ====================

def log_callback(callback_type, data):
    """记录回调到专用日志文件"""
    try:
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "callback_type": callback_type,
            "data": data
        }
        
        with open(Config.CALLBACK_LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
            
    except Exception as e:
        logger.error(f"记录回调日志失败: {str(e)}")

if __name__ == '__main__':
    logger.info("启动电子病历生成系统 - 录制服务器")
    logger.info(f"SRS录制目录: {Config.SRS_RECORDING_DIR}")
    logger.info(f"回调日志文件: {Config.CALLBACK_LOG_FILE}")
    
    # 启动Flask应用
    app.run(
        host='0.0.0.0',
        port=5400,
        debug=True,
        threaded=True
    )
"""

# 文件结束 - 所有代码已被注释
# 如需重新启用SRS API回调服务，请取消注释并参考服务管理操作指南
