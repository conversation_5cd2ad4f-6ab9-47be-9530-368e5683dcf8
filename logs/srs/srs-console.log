[2025-08-14 14:53:32.066][INFO][607300][57o84c06] XCORE-SRS/6.0.134(Hang)
[2025-08-14 14:53:32.067][INFO][607300][57o84c06] config parse complete
[2025-08-14 14:53:32.067][INFO][607300][57o84c06] write log to console
[2025-08-14 14:53:32.067][INFO][607300][57o84c06] SRS/6.0.134(Hang), MIT
[2025-08-14 14:53:32.067][INFO][607300][57o84c06] authors: <PERSON><PERSON><<EMAIL>> <PERSON><PERSON><PERSON><PERSON><<EMAIL>> Win<PERSON><<EMAIL>> <PERSON><PERSON><PERSON><PERSON><PERSON><<EMAIL>> Shi<PERSON>ei<<EMAIL>> <PERSON><PERSON><PERSON><PERSON><<EMAIL>> Wu<PERSON><PERSON>qi<PERSON><<EMAIL>> <PERSON>a<PERSON><PERSON><PERSON><<EMAIL>> Li<PERSON><PERSON><<EMAIL>> ChenGuanghua<<EMAIL>> ChenHaibo<<EMAIL>> ZhangJunqin<<EMAIL>> and https://github.com/ossrs/srs/blob/develop/trunk/AUTHORS.md#contributors
[2025-08-14 14:53:32.067][INFO][607300][57o84c06] cwd=/data/MedicalRecord/srs/trunk, work_dir=./, build: 2025-08-08 11:41:03, configure: --rtc=on --srt=on --gb28181=on, uname: Linux gpuserver 5.4.0-216-generic #236-Ubuntu SMP Fri Apr 11 19:53:21 UTC 2025 x86_64 x86_64 x86_64 GNU/Linux, osx: 0, env: 0, pkg: 
[2025-08-14 14:53:32.067][INFO][607300][57o84c06] configure detail: --prefix=/usr/local/srs --config=conf/srs.conf --osx=off --hls=on --hds=off --dvr=on --ssl=on --https=on --ssl-1-0=off --ssl-local=off --sys-ssl=off --transcode=on --ingest=on --stat=on --http-callback=on --http-server=on --stream-converter=on --http-api=on --utest=off --srt=on --sys-srt=off --rtc=on --h265=on --gb28181=on --simulator=off --cxx11=on --cxx14=off --backtrace=on --ffmpeg-fit=on --sys-ffmpeg=off --ffmpeg-opus=off --nasm=on --srtp-nasm=on --sys-srtp=off --clean=on --gperf=off --gmc=off --gmd=off --gmp=off --gcp=off --gprof=off --static=off --shared-st=off --shared-srt=reserved --shared-ffmpeg=reserved --shared-srtp=reserved --log-verbose=off --log-info=off --log-trace=on --log-level_v2=on --gcov=off --apm=off --debug=off --debug-stats=off --cross-build=off --sanitizer=on --sanitizer-static=off --sanitizer-log=off --cygwin64=off --single-thread=off --generic-linux=off --build-cache=on --cc=gcc --cxx=g++ --ar=ar --ld=ld --randlib=randlib
[2025-08-14 14:53:32.067][INFO][607300][57o84c06] srs checking config...
[2025-08-14 14:53:32.069][INFO][607300][57o84c06] ips, iface[0] ens3f1 ipv4 0x11043 **************, iface[1] br-77862bd1813b ipv4 0x11043 **********, iface[2] br-bd7e956adfeb ipv4 0x11043 **********, iface[3] ens3f1 ipv6 0x11043 fe80::8e2a:8eff:fe3d:3531%ens3f1, iface[4] br-77862bd1813b ipv6 0x11043 fe80::42:caff:fed9:17f7%br-77862bd1813b, iface[5] br-bd7e956adfeb ipv6 0x11043 fe80::42:64ff:fe53:d638%br-bd7e956adfeb, iface[6] vethb45f2f0 ipv6 0x11043 fe80::a086:90ff:fee6:4b11%vethb45f2f0, iface[7] vethbc0e062 ipv6 0x11043 fe80::2c97:4aff:fe00:c84%vethbc0e062, iface[8] veth98dddd2 ipv6 0x11043 fe80::f4dc:c6ff:fe88:7279%veth98dddd2, iface[9] veth3b0b8b8 ipv6 0x11043 fe80::8021:6bff:fe70:c149%veth3b0b8b8, iface[10] vethafa1b79 ipv6 0x11043 fe80::d875:93ff:fe69:d4a6%vethafa1b79, iface[11] veth932f740 ipv6 0x11043 fe80::a4b5:abff:fedc:ab87%veth932f740, iface[12] vethc54d673 ipv6 0x11043 fe80::101f:d6ff:fe0f:6d11%vethc54d673, iface[13] vethedad21e ipv6 0x11043 fe80::bc68:dff:fecd:859c%vethedad21e, iface[14] veth7b23df4 ipv6 0x11043 fe80::88fc:6fff:feaf:d79f%veth7b23df4, iface[15] vethb54ad99 ipv6 0x11043 fe80::682a:b4ff:fe4d:2a8a%vethb54ad99, iface[16] veth3a5bff5 ipv6 0x11043 fe80::741a:6bff:fe75:1889%veth3a5bff5, iface[17] vethd03dd95 ipv6 0x11043 fe80::7cd4:40ff:fe0e:bee1%vethd03dd95, iface[18] veth833e31f ipv6 0x11043 fe80::7048:e4ff:fe7b:5d48%veth833e31f
[2025-08-14 14:53:32.069][INFO][607300][57o84c06] devices, intranet ens3f1 **************, intranet br-77862bd1813b **********, intranet br-bd7e956adfeb **********, intranet ens3f1 fe80::8e2a:8eff:fe3d:3531%ens3f1, intranet br-77862bd1813b fe80::42:caff:fed9:17f7%br-77862bd1813b, intranet br-bd7e956adfeb fe80::42:64ff:fe53:d638%br-bd7e956adfeb, intranet vethb45f2f0 fe80::a086:90ff:fee6:4b11%vethb45f2f0, intranet vethbc0e062 fe80::2c97:4aff:fe00:c84%vethbc0e062, intranet veth98dddd2 fe80::f4dc:c6ff:fe88:7279%veth98dddd2, intranet veth3b0b8b8 fe80::8021:6bff:fe70:c149%veth3b0b8b8, intranet vethafa1b79 fe80::d875:93ff:fe69:d4a6%vethafa1b79, intranet veth932f740 fe80::a4b5:abff:fedc:ab87%veth932f740, intranet vethc54d673 fe80::101f:d6ff:fe0f:6d11%vethc54d673, intranet vethedad21e fe80::bc68:dff:fecd:859c%vethedad21e, intranet veth7b23df4 fe80::88fc:6fff:feaf:d79f%veth7b23df4, intranet vethb54ad99 fe80::682a:b4ff:fe4d:2a8a%vethb54ad99, intranet veth3a5bff5 fe80::741a:6bff:fe75:1889%veth3a5bff5, intranet vethd03dd95 fe80::7cd4:40ff:fe0e:bee1%vethd03dd95, intranet veth833e31f fe80::7048:e4ff:fe7b:5d48%veth833e31f
[33m[2025-08-14 14:53:32.069][WARN][607300][57o84c06][22] stats network use index=0, ip=**************, ifname=ens3f1
[0m[33m[2025-08-14 14:53:32.069][WARN][607300][57o84c06][22] stats disk not configed, disk iops disabled.
[0m[2025-08-14 14:53:32.069][INFO][607300][57o84c06] write log to console
[2025-08-14 14:53:32.070][INFO][607300][57o84c06] features, rch:on, dash:on, hls:on, hds:off, srt:on, hc:on, ha:on, hs:on, hp:on, dvr:on, trans:on, inge:on, stat:on, sc:on
[2025-08-14 14:53:32.070][INFO][607300][57o84c06] SRS on amd64 x86_64, conf:/data/MedicalRecord/srs/trunk/conf/rtc.conf, limit:1000, writev:1024, encoding:little-endian, HZ:100
[2025-08-14 14:53:32.070][INFO][607300][57o84c06] mw sleep:350ms. mr enabled:on, default:0, sleep:350ms
[2025-08-14 14:53:32.070][INFO][607300][57o84c06] gc:on, pq:30000ms, cscc:[0,16), csa:on, tn:on(may hurts performance), ss:auto(guess by merged write)
[2025-08-14 14:53:32.070][INFO][607300][57o84c06] system default latency(ms): mw(0-350) + mr(0-350) + play-queue(0-30000)
[33m[2025-08-14 14:53:32.070][WARN][607300][57o84c06][22] SRS/6.0.134 is not stable
[0m[2025-08-14 14:53:32.070][INFO][607300][57o84c06] write pid=607300 to ./objs/srs.pid success!
[2025-08-14 14:53:32.070][INFO][607300][57o84c06] Thread #1(primordial): init name=srs-master-1, interval=5000ms
[2025-08-14 14:53:32.070][INFO][607300][57o84c06] Pool: Start threads primordial=1, hybrids=1 ok
[2025-08-14 14:53:32.070][INFO][607300][2y55t0sw] Thread #2: run with tid=607302, entry=0x60c000000700, label=hybrid, name=srs-hybrid-2
[2025-08-14 14:53:32.089][INFO][607300][2y55t0sw] fingerprint=66:CF:6E:3E:8F:63:77:AE:D0:C0:67:B0:E7:4B:01:45:0D:4E:38:C2:74:09:FF:BE:D0:4C:06:1B:06:17:55:67
[2025-08-14 14:53:32.089][INFO][607300][2y55t0sw] CircuitBreaker: enabled=1, high=2x90, critical=1x95, dying=5x99
[2025-08-14 14:53:32.089][INFO][607300][2y55t0sw] http flv live stream, vhost=__defaultVhost__, mount=[vhost]/[app]/[stream].flv
[2025-08-14 14:53:32.089][INFO][607300][2y55t0sw] http: root mount to ./objs/nginx/html
[2025-08-14 14:53:32.089][INFO][607300][2y55t0sw] server main cid=2y55t0sw, pid=607300, ppid=607194, asprocess=0
[2025-08-14 14:53:32.090][INFO][607300][2y55t0sw] RTMP listen at tcp://0.0.0.0:1935, fd=9
[2025-08-14 14:53:32.090][INFO][607300][2y55t0sw] HTTP-API listen at tcp://0.0.0.0:1985, fd=10
[2025-08-14 14:53:32.090][INFO][607300][2y55t0sw] HTTP-Server listen at tcp://0.0.0.0:8080, fd=11
[2025-08-14 14:53:32.090][INFO][607300][2y55t0sw] signal installed, reload=1, reopen=10, fast_quit=15, grace_quit=3
[2025-08-14 14:53:32.090][INFO][607300][2y55t0sw] http: api mount /console to ./objs/nginx/html/console
[2025-08-14 14:53:32.091][INFO][607300][2y55t0sw] rtc listen at udp://0.0.0.0:8000, fd=12
[2025-08-14 14:53:32.092][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,38MB
[2025-08-14 14:53:32.092][INFO][607300][u4rb4168] TCP: connection manager run, conns=0
[2025-08-14 14:53:32.092][INFO][607300][0095v383] GB: connection manager run, conns=0
[2025-08-14 14:53:32.093][INFO][607300][24009hzm] SRT: connection manager run, conns=0
[2025-08-14 14:53:32.093][INFO][607300][r928i43c] UDP #12 LISTEN at 0.0.0.0:8000, SO_SNDBUF(default=212992, expect=10485760, actual=20971520, r0=0), SO_RCVBUF(default=212992, expect=10485760, actual=20971520, r0=0)
[2025-08-14 14:53:32.093][INFO][607300][i036lpvl] RTC: connection manager run, conns=0
[2025-08-14 14:53:37.071][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,38MB
[2025-08-14 14:53:37.075][INFO][607300][57o84c06] Process: cpu=0.00%,38MB, threads=2
[2025-08-14 14:53:42.071][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,39MB
[2025-08-14 14:53:42.080][INFO][607300][57o84c06] Process: cpu=0.00%,39MB, threads=2
[2025-08-14 14:53:47.072][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,39MB, cid=1,1, timer=62,0,0, clock=1,49,1,0,0,0,0,0,0
[2025-08-14 14:53:47.085][INFO][607300][57o84c06] Process: cpu=0.00%,39MB, threads=2
[2025-08-14 14:53:52.073][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,39MB, cid=1,1, timer=62,0,0, clock=1,49,1,0,0,0,0,0,0
[2025-08-14 14:53:52.090][INFO][607300][57o84c06] Process: cpu=0.00%,39MB, threads=2
[2025-08-14 14:53:57.073][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,39MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:53:57.095][INFO][607300][57o84c06] Process: cpu=0.00%,39MB, threads=2
[2025-08-14 14:54:02.074][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,39MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:54:02.100][INFO][607300][57o84c06] Process: cpu=0.00%,39MB, threads=2
[2025-08-14 14:54:07.075][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,39MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:54:07.106][INFO][607300][57o84c06] Process: cpu=0.00%,39MB, threads=2
[2025-08-14 14:54:12.075][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,39MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:54:12.111][INFO][607300][57o84c06] Process: cpu=0.00%,39MB, threads=2
[2025-08-14 14:54:17.075][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,39MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:54:17.116][INFO][607300][57o84c06] Process: cpu=0.00%,39MB, threads=2
[2025-08-14 14:54:22.075][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,39MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:54:22.121][INFO][607300][57o84c06] Process: cpu=0.00%,39MB, threads=2
[2025-08-14 14:54:27.076][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,40MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:54:27.127][INFO][607300][57o84c06] Process: cpu=0.00%,40MB, threads=2
[2025-08-14 14:54:32.077][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,40MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:54:32.132][INFO][607300][57o84c06] Process: cpu=0.00%,40MB, threads=2
[2025-08-14 14:54:37.077][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,40MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:54:37.137][INFO][607300][57o84c06] Process: cpu=1.00%,40MB, threads=2
[2025-08-14 14:54:42.077][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,40MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:54:42.142][INFO][607300][57o84c06] Process: cpu=1.00%,40MB, threads=2
[2025-08-14 14:54:47.078][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,40MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:54:47.147][INFO][607300][57o84c06] Process: cpu=1.00%,40MB, threads=2
[2025-08-14 14:54:52.079][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,40MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:54:52.153][INFO][607300][57o84c06] Process: cpu=0.00%,40MB, threads=2
[2025-08-14 14:54:57.079][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,40MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:54:57.158][INFO][607300][57o84c06] Process: cpu=0.00%,40MB, threads=2
[2025-08-14 14:55:02.079][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,40MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:55:02.163][INFO][607300][57o84c06] Process: cpu=0.00%,40MB, threads=2
[2025-08-14 14:55:07.079][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,40MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:55:07.168][INFO][607300][57o84c06] Process: cpu=0.00%,40MB, threads=2
[2025-08-14 14:55:12.080][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,40MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:55:12.173][INFO][607300][57o84c06] Process: cpu=1.00%,40MB, threads=2
[2025-08-14 14:55:17.081][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,40MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:55:17.179][INFO][607300][57o84c06] Process: cpu=1.00%,40MB, threads=2
[2025-08-14 14:55:22.082][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,40MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:55:22.184][INFO][607300][57o84c06] Process: cpu=0.00%,40MB, threads=2
[2025-08-14 14:55:27.083][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:55:27.189][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:55:32.083][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:55:32.194][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:55:37.084][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:55:37.199][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:55:42.085][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:55:42.204][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:55:47.085][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:55:47.210][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:55:52.086][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:55:52.215][INFO][607300][57o84c06] Process: cpu=1.00%,41MB, threads=2
[2025-08-14 14:55:57.087][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:55:57.220][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:56:02.087][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:56:02.225][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:56:07.087][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:56:07.230][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:56:12.088][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:56:12.235][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:56:17.088][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,41MB, cid=1,0, timer=63,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:56:17.240][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:56:22.088][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,41MB, cid=1,0, timer=63,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:56:22.245][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:56:27.089][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:56:27.251][INFO][607300][57o84c06] Process: cpu=0.00%,41MB, threads=2
[2025-08-14 14:56:32.089][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,41MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:56:32.256][INFO][607300][57o84c06] Process: cpu=0.00%,42MB, threads=2
[2025-08-14 14:56:37.090][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:56:37.261][INFO][607300][57o84c06] Process: cpu=0.00%,42MB, threads=2
[2025-08-14 14:56:42.090][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:56:42.265][INFO][607300][57o84c06] Process: cpu=0.00%,42MB, threads=2
[2025-08-14 14:56:47.090][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:56:47.271][INFO][607300][57o84c06] Process: cpu=1.00%,42MB, threads=2
[2025-08-14 14:56:52.091][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:56:52.276][INFO][607300][57o84c06] Process: cpu=1.00%,42MB, threads=2
[2025-08-14 14:56:57.091][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:56:57.281][INFO][607300][57o84c06] Process: cpu=0.00%,42MB, threads=2
[2025-08-14 14:57:02.091][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:57:02.286][INFO][607300][57o84c06] Process: cpu=1.00%,42MB, threads=2
[2025-08-14 14:57:07.092][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:57:07.291][INFO][607300][57o84c06] Process: cpu=0.00%,42MB, threads=2
[2025-08-14 14:57:12.092][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:57:12.296][INFO][607300][57o84c06] Process: cpu=1.00%,42MB, threads=2
[2025-08-14 14:57:17.093][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:57:17.301][INFO][607300][57o84c06] Process: cpu=1.00%,42MB, threads=2
[2025-08-14 14:57:22.093][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:57:22.305][INFO][607300][57o84c06] Process: cpu=0.00%,42MB, threads=2
[2025-08-14 14:57:27.094][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:57:27.310][INFO][607300][57o84c06] Process: cpu=1.00%,42MB, threads=2
[2025-08-14 14:57:32.094][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,42MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:57:32.314][INFO][607300][57o84c06] Process: cpu=0.00%,42MB, threads=2
[2025-08-14 14:57:37.095][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:57:37.319][INFO][607300][57o84c06] Process: cpu=0.00%,42MB, threads=2
[2025-08-14 14:57:42.096][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,42MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:57:42.325][INFO][607300][57o84c06] Process: cpu=1.00%,42MB, threads=2
[2025-08-14 14:57:47.097][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:57:47.329][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:57:52.097][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:57:52.334][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:57:57.098][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:57:57.339][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:58:02.098][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:58:02.344][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:58:07.099][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:58:07.349][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:58:12.099][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:58:12.354][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:58:17.100][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:58:17.358][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:58:22.100][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:58:22.363][INFO][607300][57o84c06] Process: cpu=1.00%,43MB, threads=2
[2025-08-14 14:58:27.101][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:58:27.368][INFO][607300][57o84c06] Process: cpu=1.00%,43MB, threads=2
[2025-08-14 14:58:32.101][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:58:32.373][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:58:37.102][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:58:37.378][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:58:42.102][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:58:42.383][INFO][607300][57o84c06] Process: cpu=1.00%,43MB, threads=2
[2025-08-14 14:58:47.103][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:58:47.388][INFO][607300][57o84c06] Process: cpu=1.00%,43MB, threads=2
[2025-08-14 14:58:52.103][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:58:52.394][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:58:57.103][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:58:57.399][INFO][607300][57o84c06] Process: cpu=1.00%,43MB, threads=2
[2025-08-14 14:59:02.103][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:59:02.404][INFO][607300][57o84c06] Process: cpu=1.00%,43MB, threads=2
[2025-08-14 14:59:07.104][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,43MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:59:07.409][INFO][607300][57o84c06] Process: cpu=0.00%,43MB, threads=2
[2025-08-14 14:59:12.104][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:59:12.414][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 14:59:17.104][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:59:17.420][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 14:59:22.105][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:59:22.425][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 14:59:27.106][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 14:59:27.430][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 14:59:32.106][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:59:32.435][INFO][607300][57o84c06] Process: cpu=2.00%,44MB, threads=2
[2025-08-14 14:59:37.106][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:59:37.440][INFO][607300][57o84c06] Process: cpu=1.00%,44MB, threads=2
[2025-08-14 14:59:42.106][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:59:42.445][INFO][607300][57o84c06] Process: cpu=1.00%,44MB, threads=2
[2025-08-14 14:59:47.107][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:59:47.450][INFO][607300][57o84c06] Process: cpu=1.00%,44MB, threads=2
[2025-08-14 14:59:52.107][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:59:52.456][INFO][607300][57o84c06] Process: cpu=1.00%,44MB, threads=2
[2025-08-14 14:59:57.108][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,44MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 14:59:57.461][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 15:00:02.109][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:00:02.466][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 15:00:07.109][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:00:07.471][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 15:00:12.110][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:00:12.476][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 15:00:17.111][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:00:17.481][INFO][607300][57o84c06] Process: cpu=1.00%,44MB, threads=2
[2025-08-14 15:00:22.112][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:00:22.486][INFO][607300][57o84c06] Process: cpu=1.00%,44MB, threads=2
[2025-08-14 15:00:27.112][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:00:27.491][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 15:00:32.113][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:00:32.497][INFO][607300][57o84c06] Process: cpu=1.00%,44MB, threads=2
[2025-08-14 15:00:37.114][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:00:37.502][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 15:00:42.114][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:00:42.507][INFO][607300][57o84c06] Process: cpu=1.00%,44MB, threads=2
[2025-08-14 15:00:47.115][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,44MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:00:47.512][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 15:00:52.115][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,44MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:00:52.517][INFO][607300][57o84c06] Process: cpu=0.00%,44MB, threads=2
[2025-08-14 15:00:57.116][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:00:57.523][INFO][607300][57o84c06] Process: cpu=1.00%,45MB, threads=2
[2025-08-14 15:01:02.116][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=63,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:01:02.527][INFO][607300][57o84c06] Process: cpu=1.00%,45MB, threads=2
[2025-08-14 15:01:07.116][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:01:07.533][INFO][607300][57o84c06] Process: cpu=1.00%,45MB, threads=2
[2025-08-14 15:01:12.117][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:01:12.537][INFO][607300][57o84c06] Process: cpu=0.00%,45MB, threads=2
[2025-08-14 15:01:17.117][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:01:17.541][INFO][607300][57o84c06] Process: cpu=1.00%,45MB, threads=2
[2025-08-14 15:01:22.117][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:01:22.547][INFO][607300][57o84c06] Process: cpu=0.00%,45MB, threads=2
[2025-08-14 15:01:27.118][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:01:27.552][INFO][607300][57o84c06] Process: cpu=0.00%,45MB, threads=2
[2025-08-14 15:01:32.119][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:01:32.557][INFO][607300][57o84c06] Process: cpu=0.00%,45MB, threads=2
[2025-08-14 15:01:37.120][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:01:37.562][INFO][607300][57o84c06] Process: cpu=0.00%,45MB, threads=2
[2025-08-14 15:01:42.120][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:01:42.568][INFO][607300][57o84c06] Process: cpu=0.00%,45MB, threads=2
[2025-08-14 15:01:47.121][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:01:47.573][INFO][607300][57o84c06] Process: cpu=0.00%,45MB, threads=2
[2025-08-14 15:01:52.121][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:01:52.578][INFO][607300][57o84c06] Process: cpu=0.00%,45MB, threads=2
[2025-08-14 15:01:57.122][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:01:57.583][INFO][607300][57o84c06] Process: cpu=1.00%,45MB, threads=2
[2025-08-14 15:02:02.122][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:02:02.588][INFO][607300][57o84c06] Process: cpu=1.00%,45MB, threads=2
[2025-08-14 15:02:07.123][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,45MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:02:07.594][INFO][607300][57o84c06] Process: cpu=0.00%,45MB, threads=2
[2025-08-14 15:02:09.660][INFO][607300][61668t2t] HTTP #0 **************:61922 OPTIONS http://**************:1985/rtc/v1/publish/, content-length=-1
[2025-08-14 15:02:09.665][INFO][607300][61668t2t] TCP: before dispose resource(HttpConn)(0x607000005650), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:02:09.665][WARN][607300][61668t2t][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:02:09.665][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:02:09.665][INFO][607300][61668t2t] TCP: disposing #0 resource(HttpConn)(0x607000005650), conns=1, disposing=1, zombies=0
[2025-08-14 15:02:09.669][INFO][607300][t27e37y5] HTTP #0 **************:61923 POST http://**************:1985/rtc/v1/publish/, content-length=6383
[2025-08-14 15:02:11.828][INFO][607300][t27e37y5] RTC publish webrtc://**************:8000/live/test0814, api=http://**************:1985/rtc/v1/publish/, tid=4d97979, clientip=**************, app=live, stream=test0814, offer=5879B, eip=, codec=
[2025-08-14 15:02:11.828][INFO][607300][t27e37y5] ignore attribute=, value=
[2025-08-14 15:02:11.830][INFO][607300][t27e37y5] new rtc source, stream_url=/live/test0814
[2025-08-14 15:02:11.831][INFO][607300][t27e37y5] RTC publisher nack=1, nnc=1, pt-drop=0, twcc=1/3
[2025-08-14 15:02:11.832][INFO][607300][t27e37y5] free rtc source id=[]
[2025-08-14 15:02:11.832][INFO][607300][t27e37y5] new live source, stream_url=/live/test0814
[2025-08-14 15:02:11.833][INFO][607300][t27e37y5] RTC: Init tracks {track: d8d9a794-e218-46b5-8167-175869375faa, is_active: 0=>1},{track: 254b8cfe-974a-4cad-b95a-d15884286735, is_active: 0=>1}, ok
[2025-08-14 15:02:11.833][INFO][607300][t27e37y5] RTC: Use candidates **************, ************, protocol=udp
[2025-08-14 15:02:11.837][INFO][607300][t27e37y5] RTC init session, user=07ilz84n:EQuT, url=/live/test0814, encrypt=1/1, DTLS(role=passive, version=auto), timeout=30000ms, nack=1
[2025-08-14 15:02:11.838][INFO][607300][t27e37y5] RTC username=07ilz84n:EQuT, offer=5879B, answer=1605B
[2025-08-14 15:02:11.838][INFO][607300][t27e37y5] RTC remote offer: v=0\r\no=- 6307261190866284196 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS bdfc62a7-471b-4b38-b612-dd309cb634b4\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:EQuT\r\na=ice-pwd:3EyL1qKtWpvBV327VQdlIbDP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 ED:9A:1C:0C:D8:9A:1A:34:29:77:7E:F9:C7:2B:96:8E:EB:55:CB:F0:F0:10:F2:9F:7F:22:A4:27:6C:55:9B:77\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:bdfc62a7-471b-4b38-b612-dd309cb634b4 254b8cfe-974a-4cad-b95a-d15884286735\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 minptime=10;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:9 G722/8000\r\na=rtpmap:0 PCMU/8000\r\na=rtpmap:8 PCMA/8000\r\na=rtpmap:13 CN/8000\r\na=rtpmap:110 telephone-event/48000\r\na=rtpmap:126 telephone-event/8000\r\na=ssrc:2629148950 cname:ZPC6MTgrH0Xv2FCS\r\na=ssrc:2629148950 msid:bdfc62a7-471b-4b38-b612-dd309cb634b4 254b8cfe-974a-4cad-b95a-d15884286735\r\nm=video 9 UDP/TLS/RTP/SAVPF 96 97 103 104 107 108 109 114 115 116 117 118 39 40 45 46 98 99 100 101 119 120 49 50 123 124 125\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:EQuT\r\na=ice-pwd:3EyL1qKtWpvBV327VQdlIbDP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 ED:9A:1C:0C:D8:9A:1A:34:29:77:7E:F9:C7:2B:96:8E:EB:55:CB:F0:F0:10:F2:9F:7F:22:A4:27:6C:55:9B:77\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:13 urn:3gpp:video-orientation\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendrecv\r\na=msid:bdfc62a7-471b-4b38-b612-dd309cb634b4 d8d9a794-e218-46b5-8167-175869375faa\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:96 VP8/90000\r\na=rtcp-fb:96 goog-remb\r\na=rtcp-fb:96 transport-cc\r\na=rtcp-fb:96 ccm fir\r\na=rtcp-fb:96 nack\r\na=rtcp-fb:96 nack pli\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:103 H264/90000\r\na=rtcp-fb:103 goog-remb\r\na=rtcp-fb:103 transport-cc\r\na=rtcp-fb:103 ccm fir\r\na=rtcp-fb:103 nack\r\na=rtcp-fb:103 nack pli\r\na=fmtp:103 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f\r\na=rtpmap:104 rtx/90000\r\na=fmtp:104 apt=103\r\na=rtpmap:107 H264/90000\r\na=rtcp-fb:107 goog-remb\r\na=rtcp-fb:107 transport-cc\r\na=rtcp-fb:107 ccm fir\r\na=rtcp-fb:107 nack\r\na=rtcp-fb:107 nack pli\r\na=fmtp:107 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42001f\r\na=rtpmap:108 rtx/90000\r\na=fmtp:108 apt=107\r\na=rtpmap:109 H264/90000\r\na=rtcp-fb:109 goog-remb\r\na=rtcp-fb:109 transport-cc\r\na=rtcp-fb:109 ccm fir\r\na=rtcp-fb:109 nack\r\na=rtcp-fb:109 nack pli\r\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\r\na=rtpmap:114 rtx/90000\r\na=fmtp:114 apt=109\r\na=rtpmap:115 H264/90000\r\na=rtcp-fb:115 goog-remb\r\na=rtcp-fb:115 transport-cc\r\na=rtcp-fb:115 ccm fir\r\na=rtcp-fb:115 nack\r\na=rtcp-fb:115 nack pli\r\na=fmtp:115 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f\r\na=rtpmap:116 rtx/90000\r\na=fmtp:116 apt=115\r\na=rtpmap:117 H264/90000\r\na=rtcp-fb:117 goog-remb\r\na=rtcp-fb:117 transport-cc\r\na=rtcp-fb:117 ccm fir\r\na=rtcp-fb:117 nack\r\na=rtcp-fb:117 nack pli\r\na=fmtp:117 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\r\na=rtpmap:118 rtx/90000\r\na=fmtp:118 apt=117\r\na=rtpmap:39 H264/90000\r\na=rtcp-fb:39 goog-remb\r\na=rtcp-fb:39 transport-cc\r\na=rtcp-fb:39 ccm fir\r\na=rtcp-fb:39 nack\r\na=rtcp-fb:39 nack pli\r\na=fmtp:39 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=4d001f\r\na=rtpmap:40 rtx/90000\r\na=fmtp:40 apt=39\r\na=rtpmap:45 AV1/90000\r\na=rtcp-fb:45 goog-remb\r\na=rtcp-fb:45 transport-cc\r\na=rtcp-fb:45 ccm fir\r\na=rtcp-fb:45 nack\r\na=rtcp-fb:45 nack pli\r\na=fmtp:45 level-idx=5;profile=0;tier=0\r\na=rtpmap:46 rtx/90000\r\na=fmtp:46 apt=45\r\na=rtpmap:98 VP9/90000\r\na=rtcp-fb:98 goog-remb\r\na=rtcp-fb:98 transport-cc\r\na=rtcp-fb:98 ccm fir\r\na=rtcp-fb:98 nack\r\na=rtcp-fb:98 nack pli\r\na=fmtp:98 profile-id=0\r\na=rtpmap:99 rtx/90000\r\na=fmtp:99 apt=98\r\na=rtpmap:100 VP9/90000\r\na=rtcp-fb:100 goog-remb\r\na=rtcp-fb:100 transport-cc\r\na=rtcp-fb:100 ccm fir\r\na=rtcp-fb:100 nack\r\na=rtcp-fb:100 nack pli\r\na=fmtp:100 profile-id=2\r\na=rtpmap:101 rtx/90000\r\na=fmtp:101 apt=100\r\na=rtpmap:119 H264/90000\r\na=rtcp-fb:119 goog-remb\r\na=rtcp-fb:119 transport-cc\r\na=rtcp-fb:119 ccm fir\r\na=rtcp-fb:119 nack\r\na=rtcp-fb:119 nack pli\r\na=fmtp:119 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=64001f\r\na=rtpmap:120 rtx/90000\r\na=fmtp:120 apt=119\r\na=rtpmap:49 H265/90000\r\na=rtcp-fb:49 goog-remb\r\na=rtcp-fb:49 transport-cc\r\na=rtcp-fb:49 ccm fir\r\na=rtcp-fb:49 nack\r\na=rtcp-fb:49 nack pli\r\na=fmtp:49 level-id=93;profile-id=1;tier-flag=0;tx-mode=SRST\r\na=rtpmap:50 rtx/90000\r\na=fmtp:50 apt=49\r\na=rtpmap:123 red/90000\r\na=rtpmap:124 rtx/90000\r\na=fmtp:124 apt=123\r\na=rtpmap:125 ulpfec/90000\r\na=ssrc-group:FID 4106216783 2733719918\r\na=ssrc:4106216783 cname:ZPC6MTgrH0Xv2FCS\r\na=ssrc:4106216783 msid:bdfc62a7-471b-4b38-b612-dd309cb634b4 d8d9a794-e218-46b5-8167-175869375faa\r\na=ssrc:2733719918 cname:ZPC6MTgrH0Xv2FCS\r\na=ssrc:2733719918 msid:bdfc62a7-471b-4b38-b612-dd309cb634b4 d8d9a794-e218-46b5-8167-175869375faa\r\n
[2025-08-14 15:02:11.838][INFO][607300][t27e37y5] RTC local answer: v=0\r\no=SRS/6.0.134(Hang) 107545981158528 2 IN IP4 0.0.0.0\r\ns=SRSPublishSession\r\nt=0 0\r\na=ice-lite\r\na=group:BUNDLE 0 1\r\na=msid-semantic: WMS live/test0814\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:07ilz84n\r\na=ice-pwd:1m284809or6np5990s92j47k29s8z890\r\na=fingerprint:sha-256 66:CF:6E:3E:8F:63:77:AE:D0:C0:67:B0:E7:4B:01:45:0D:4E:38:C2:74:09:FF:BE:D0:4C:06:1B:06:17:55:67\r\na=setup:passive\r\na=mid:0\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 minptime=10;useinbandfec=1\r\na=candidate:0 1 udp 2130706431 ************** 8000 typ host generation 0\r\na=candidate:1 1 udp 2130706431 ************ 8000 typ host generation 0\r\nm=video 9 UDP/TLS/RTP/SAVPF 109 123\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:07ilz84n\r\na=ice-pwd:1m284809or6np5990s92j47k29s8z890\r\na=fingerprint:sha-256 66:CF:6E:3E:8F:63:77:AE:D0:C0:67:B0:E7:4B:01:45:0D:4E:38:C2:74:09:FF:BE:D0:4C:06:1B:06:17:55:67\r\na=setup:passive\r\na=mid:1\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:109 H264/90000\r\na=rtcp-fb:109 transport-cc\r\na=rtcp-fb:109 nack\r\na=rtcp-fb:109 nack pli\r\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\r\na=rtpmap:123 red/90000\r\na=candidate:0 1 udp 2130706431 ************** 8000 typ host generation 0\r\na=candidate:1 1 udp 2130706431 ************ 8000 typ host generation 0\r\n
[2025-08-14 15:02:11.846][INFO][607300][t27e37y5] TCP: before dispose resource(HttpConn)(0x607000006a70), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:02:11.846][WARN][607300][t27e37y5][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:02:11.846][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:02:11.846][INFO][607300][t27e37y5] TCP: disposing #0 resource(HttpConn)(0x607000006a70), conns=1, disposing=1, zombies=0
[2025-08-14 15:02:11.863][INFO][607300][t27e37y5] RTC: session address init **************:57670
[2025-08-14 15:02:11.864][INFO][607300][t27e37y5] RTC: session STUN done, waiting DTLS handshake.
[2025-08-14 15:02:11.864][INFO][607300][r928i43c] <- RTC RECV #12, udp 1, pps 0/0, schedule 1
[2025-08-14 15:02:11.868][INFO][607300][t27e37y5] DTLS: State Passive RECV, done=0, arq=0, r0=153, len=153, cnt=22, size=140, hs=1
[2025-08-14 15:02:11.869][INFO][607300][t27e37y5] DTLS: State Passive SEND, done=0, arq=0, r0=0, len=708, cnt=22, size=110, hs=2
[2025-08-14 15:02:11.875][INFO][607300][t27e37y5] DTLS: State Passive RECV, done=0, arq=0, r0=579, len=579, cnt=22, size=299, hs=11
[2025-08-14 15:02:11.876][INFO][607300][t27e37y5] DTLS: State Passive SEND, done=0, arq=0, r0=0, len=75, cnt=20, size=1, hs=1
[2025-08-14 15:02:11.876][INFO][607300][t27e37y5] RTC: DTLS handshake done.
[2025-08-14 15:02:11.876][INFO][607300][t27e37y5] RTC: session pub=1, sub=0, to=30000ms connection established
[2025-08-14 15:02:11.876][INFO][607300][t27e37y5] RTC: Publisher url=/live/test0814 established
[2025-08-14 15:02:11.879][INFO][607300][t27e37y5] ignore disabled exec for vhost=__defaultVhost__
[2025-08-14 15:02:11.879][INFO][607300][t27e37y5] http: mount flv stream for sid=/live/test0814, mount=/live/test0814.flv
[2025-08-14 15:02:11.902][INFO][607300][t27e37y5] RTC: Discard no-sync Audio, ssrc=2629148950, seq=11258, ts=2946140486, state=-1
[2025-08-14 15:02:12.095][INFO][607300][t27e37y5] NACK: update seq=20150, nack range [20149, 20150]
[2025-08-14 15:02:12.124][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,46MB, cid=4,1, timer=62,1,1, clock=0,48,1,0,1,0,0,0,0, free=1, objs=(pkt:2,raw:1,fua:1,msg:2,oth:1,buf:2)
[2025-08-14 15:02:12.124][INFO][607300][68l1dw2j] RTC: Server conns=1
[2025-08-14 15:02:12.599][INFO][607300][57o84c06] Process: cpu=2.00%,51MB, threads=2
[2025-08-14 15:02:13.352][INFO][607300][t27e37y5] RTC: Accept sync Video, ssrc=4106216783, seq=20242, ts=1171087328, state=0
[2025-08-14 15:02:13.849][INFO][607300][t27e37y5] NACK: update seq=20270, nack range [20269, 20270]
[2025-08-14 15:02:15.617][INFO][607300][t27e37y5] 7B audio sh, codec(10, profile=LC, 2channels, 0kbps, 48000HZ), flv(16bits, 2channels, 44100HZ)
[2025-08-14 15:02:15.637][INFO][607300][t27e37y5] time diff to large=4096722987, next out=4183887285, new pkt=4183887305, set to new pkt
[2025-08-14 15:02:15.916][INFO][607300][t27e37y5] 34B video sh, codec(7, profile=Baseline, level=3, 480x270, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:02:15.916][INFO][607300][t27e37y5] set ts=1171317728, header=20362, lost=20363
[33m[2025-08-14 15:02:16.169][WARN][607300][t27e37y5][11] empty nalu
[0m[2025-08-14 15:02:16.250][INFO][607300][t27e37y5] NACK: update seq=20402, nack range [20401, 20402]
[2025-08-14 15:02:17.125][INFO][607300][68l1dw2j] Hybrid cpu=4.00%,54MB, cid=4,1, timer=62,1,1, clock=0,48,1,0,1,0,0,0,0, free=1, objs=(pkt:2,raw:1,fua:1,msg:2,oth:1,buf:2)
[2025-08-14 15:02:17.125][INFO][607300][68l1dw2j] RTC: Server conns=1
[2025-08-14 15:02:17.179][INFO][607300][t27e37y5] NACK: update seq=20554, nack range [20553, 20554]
[2025-08-14 15:02:17.604][INFO][607300][57o84c06] Process: cpu=5.00%,55MB, threads=2
[2025-08-14 15:02:17.821][INFO][607300][80c8ibap] RTC: to rtmp bridge request key frame, ssrc=4106216783, publisher cid=t27e37y5
[2025-08-14 15:02:17.821][INFO][607300][80c8ibap] RTC: Need PLI ssrc=4106216783, play=[t27e37y5], publish=[t27e37y5], count=1/1
[2025-08-14 15:02:17.821][INFO][607300][t27e37y5] RTC: Request PLI ssrc=4106216783, play=[t27e37y5], count=1/1, bytes=12B
[2025-08-14 15:02:17.834][INFO][607300][t27e37y5] 34B video sh, codec(7, profile=Baseline, level=3, 480x270, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:02:17.834][INFO][607300][t27e37y5] set ts=1171490618, header=20679, lost=20680
[2025-08-14 15:02:18.169][INFO][607300][t27e37y5] NACK: update seq=20748, nack range [20747, 20748]
[2025-08-14 15:02:18.201][INFO][607300][t27e37y5] NACK: update seq=20758, nack range [20757, 20758]
[2025-08-14 15:02:18.794][INFO][607300][t27e37y5] NACK: update seq=20870, nack range [20869, 20870]
[2025-08-14 15:02:19.434][INFO][607300][t27e37y5] NACK: update seq=20985, nack range [20984, 20985]
[2025-08-14 15:02:19.865][INFO][607300][t27e37y5] NACK: update seq=21068, nack range [21067, 21068]
[2025-08-14 15:02:19.965][INFO][607300][t27e37y5] 35B video sh, codec(7, profile=Baseline, level=3.1, 640x360, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:02:19.965][INFO][607300][t27e37y5] set ts=1171682048, header=21086, lost=21087
[2025-08-14 15:02:21.754][INFO][607300][t27e37y5] NACK: update seq=21410, nack range [21409, 21410]
[2025-08-14 15:02:21.878][INFO][607300][r928i43c] <- RTC RECV #12, udp 1818, pps 3/181, schedule 1818
[2025-08-14 15:02:22.125][INFO][607300][68l1dw2j] Hybrid cpu=5.00%,60MB, cid=2,3, timer=61,10,48, clock=0,44,4,0,0,0,0,0,0, objs=(pkt:305,raw:61,fua:243,msg:350,oth:1,buf:181)
[2025-08-14 15:02:22.125][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(185,rtp:181,stun:1,rtcp:2), spkts=(14,rtp:0,stun:1,rtcp:28), rtcp=(pli:1,twcc:9,rr:1), snk=(97,a:48,v:48,h:0), fid=(id:0,fid:185,ffid:0,addr:1,faddr:185)
[2025-08-14 15:02:22.609][INFO][607300][57o84c06] Process: cpu=5.00%,61MB, threads=2
[2025-08-14 15:02:22.858][INFO][607300][t27e37y5] NACK: update seq=21615, nack range [21614, 21615]
[2025-08-14 15:02:23.195][INFO][607300][t27e37y5] NACK: update seq=21682, nack range [21681, 21682]
[2025-08-14 15:02:23.854][INFO][607300][80c8ibap] RTC: to rtmp bridge request key frame, ssrc=4106216783, publisher cid=t27e37y5
[2025-08-14 15:02:23.883][INFO][607300][t27e37y5] 35B video sh, codec(7, profile=Baseline, level=3.1, 640x360, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:02:23.883][INFO][607300][t27e37y5] set ts=1172034938, header=21819, lost=21820
[2025-08-14 15:02:24.034][INFO][607300][t27e37y5] 35B video sh, codec(7, profile=Baseline, level=3.2, 960x540, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:02:24.034][INFO][607300][t27e37y5] set ts=1172047898, header=21856, lost=21857
[2025-08-14 15:02:24.036][INFO][607300][t27e37y5] NACK: update seq=21869, nack range [21868, 21869]
[2025-08-14 15:02:24.785][INFO][607300][t27e37y5] NACK: update seq=21999, nack range [21998, 21999]
[2025-08-14 15:02:24.926][INFO][607300][t27e37y5] NACK: update seq=22028, nack range [22027, 22028]
[2025-08-14 15:02:25.149][INFO][607300][t27e37y5] NACK: update seq=22073, nack range [22072, 22073]
[2025-08-14 15:02:25.423][INFO][607300][t27e37y5] NACK: update seq=22130, nack range [22129, 22130]
[2025-08-14 15:02:26.413][INFO][607300][t27e37y5] NACK: update seq=22368, nack range [22367, 22368]
[2025-08-14 15:02:27.126][INFO][607300][68l1dw2j] Hybrid cpu=5.99%,67MB, cid=2,3, timer=61,10,48, clock=0,44,4,0,0,0,0,0,0, objs=(pkt:305,raw:61,fua:243,msg:350,oth:1,buf:181)
[2025-08-14 15:02:27.126][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(185,rtp:181,stun:1,rtcp:2), spkts=(14,rtp:0,stun:1,rtcp:28), rtcp=(pli:1,twcc:9,rr:1), snk=(97,a:48,v:48,h:0), fid=(id:0,fid:185,ffid:0,addr:1,faddr:185)
[2025-08-14 15:02:27.614][INFO][607300][57o84c06] Process: cpu=5.00%,69MB, threads=2
[2025-08-14 15:02:29.067][INFO][607300][t27e37y5] NACK: update seq=22979, nack range [22978, 22979]
[2025-08-14 15:02:29.675][INFO][607300][t27e37y5] NACK: update seq=23123, nack range [23122, 23123]
[2025-08-14 15:02:29.886][INFO][607300][80c8ibap] RTC: to rtmp bridge request key frame, ssrc=4106216783, publisher cid=t27e37y5
[2025-08-14 15:02:29.886][INFO][607300][80c8ibap] RTC: Need PLI ssrc=4106216783, play=[t27e37y5], publish=[t27e37y5], count=3/3
[2025-08-14 15:02:29.886][INFO][607300][t27e37y5] RTC: Request PLI ssrc=4106216783, play=[t27e37y5], count=3/3, bytes=12B
[2025-08-14 15:02:29.902][INFO][607300][t27e37y5] 35B video sh, codec(7, profile=Baseline, level=3.2, 960x540, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:02:29.902][INFO][607300][t27e37y5] set ts=1172576288, header=23172, lost=23173
[2025-08-14 15:02:30.829][INFO][607300][t27e37y5] NACK: update seq=23394, nack range [23393, 23394]
[2025-08-14 15:02:31.035][INFO][607300][t27e37y5] NACK: update seq=23437, nack range [23436, 23437]
[2025-08-14 15:02:31.620][INFO][607300][t27e37y5] NACK: update seq=23540, nack range [23539, 23540]
[2025-08-14 15:02:31.631][INFO][607300][t27e37y5] NACK: update seq=23573, nack range [23565, 23573]
[2025-08-14 15:02:31.637][INFO][607300][t27e37y5] NACK: update seq=12247, nack range [12245, 12247]
[2025-08-14 15:02:31.877][INFO][607300][r928i43c] <- RTC RECV #12, udp 2753, pps 8/275, schedule 2753
[2025-08-14 15:02:32.126][INFO][607300][68l1dw2j] Hybrid cpu=5.00%,75MB, cid=2,3, timer=61,10,48, clock=0,44,4,0,0,0,0,0,0, objs=(pkt:305,raw:61,fua:243,msg:350,oth:1,buf:181)
[2025-08-14 15:02:32.126][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(185,rtp:181,stun:1,rtcp:2), spkts=(14,rtp:0,stun:1,rtcp:28), rtcp=(pli:1,twcc:9,rr:1), snk=(97,a:48,v:48,h:0), fid=(id:0,fid:185,ffid:0,addr:1,faddr:185)
[33m[2025-08-14 15:02:32.206][WARN][607300][t27e37y5][11] empty nalu
[0m[2025-08-14 15:02:32.620][INFO][607300][57o84c06] Process: cpu=5.00%,76MB, threads=2
[2025-08-14 15:02:32.701][INFO][607300][t27e37y5] NACK: update seq=23768, nack range [23767, 23768]
[2025-08-14 15:02:34.659][INFO][607300][t27e37y5] NACK: update seq=24220, nack range [24219, 24220]
[2025-08-14 15:02:35.149][INFO][607300][t27e37y5] NACK: update seq=24323, nack range [24322, 24323]
[2025-08-14 15:02:35.918][INFO][607300][80c8ibap] RTC: to rtmp bridge request key frame, ssrc=4106216783, publisher cid=t27e37y5
[2025-08-14 15:02:35.955][INFO][607300][t27e37y5] 35B video sh, codec(7, profile=Baseline, level=3.2, 960x540, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:02:35.956][INFO][607300][t27e37y5] set ts=1173120698, header=24509, lost=24510
[2025-08-14 15:02:36.844][INFO][607300][t27e37y5] NACK: update seq=24718, nack range [24717, 24718]
[2025-08-14 15:02:36.994][INFO][607300][t27e37y5] NACK: update seq=24747, nack range [24746, 24747]
[2025-08-14 15:02:37.127][INFO][607300][68l1dw2j] Hybrid cpu=6.00%,82MB, cid=2,5, timer=62,11,48, clock=0,44,3,0,0,0,0,0,0, objs=(pkt:490,raw:50,fua:439,msg:560,oth:1,buf:270)
[2025-08-14 15:02:37.127][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(275,rtp:270,stun:1,rtcp:5), spkts=(14,rtp:0,stun:1,rtcp:28), rtcp=(pli:1,twcc:10,rr:1), snk=(97,a:48,v:48,h:1), fid=(id:0,fid:275,ffid:0,addr:1,faddr:275)
[2025-08-14 15:02:37.625][INFO][607300][57o84c06] Process: cpu=5.00%,83MB, threads=2
[2025-08-14 15:02:38.143][INFO][607300][t27e37y5] NACK: update seq=25014, nack range [25013, 25014]
[2025-08-14 15:02:39.950][INFO][607300][t27e37y5] NACK: update seq=25441, nack range [25440, 25441]
[2025-08-14 15:02:41.007][INFO][607300][t27e37y5] NACK: update seq=25678, nack range [25677, 25678]
[2025-08-14 15:02:41.898][INFO][607300][r928i43c] <- RTC RECV #12, udp 2825, pps 13/282, schedule 2825
[2025-08-14 15:02:41.946][INFO][607300][80c8ibap] RTC: to rtmp bridge request key frame, ssrc=4106216783, publisher cid=t27e37y5
[2025-08-14 15:02:41.946][INFO][607300][80c8ibap] RTC: Need PLI ssrc=4106216783, play=[t27e37y5], publish=[t27e37y5], count=5/5
[2025-08-14 15:02:41.946][INFO][607300][t27e37y5] RTC: Request PLI ssrc=4106216783, play=[t27e37y5], count=5/5, bytes=12B
[2025-08-14 15:02:41.970][INFO][607300][t27e37y5] 35B video sh, codec(7, profile=Baseline, level=3.2, 960x540, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:02:41.970][INFO][607300][t27e37y5] set ts=1173662138, header=25902, lost=25903
[2025-08-14 15:02:42.128][INFO][607300][68l1dw2j] Hybrid cpu=5.00%,90MB, cid=2,5, timer=62,11,48, clock=0,44,3,0,0,0,0,0,0, objs=(pkt:490,raw:50,fua:439,msg:560,oth:1,buf:270)
[2025-08-14 15:02:42.128][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(275,rtp:270,stun:1,rtcp:5), spkts=(14,rtp:0,stun:1,rtcp:28), rtcp=(pli:1,twcc:10,rr:1), snk=(97,a:48,v:48,h:1), fid=(id:0,fid:275,ffid:0,addr:1,faddr:275)
[2025-08-14 15:02:42.335][INFO][607300][t27e37y5] NACK: update seq=25990, nack range [25989, 25990]
[2025-08-14 15:02:42.630][INFO][607300][57o84c06] Process: cpu=5.00%,91MB, threads=2
[2025-08-14 15:02:43.469][INFO][607300][t27e37y5] NACK: update seq=26254, nack range [26253, 26254]
[2025-08-14 15:02:43.629][INFO][607300][t27e37y5] NACK: update seq=26293, nack range [26292, 26293]
[2025-08-14 15:02:43.838][INFO][607300][t27e37y5] NACK: update seq=26341, nack range [26339, 26341]
[2025-08-14 15:02:43.839][INFO][607300][t27e37y5] NACK: update seq=26345, nack range [26344, 26345]
[2025-08-14 15:02:44.492][INFO][607300][t27e37y5] NACK: update seq=26494, nack range [26493, 26494]
[2025-08-14 15:02:44.703][INFO][607300][t27e37y5] NACK: update seq=26539, nack range [26538, 26539]
[2025-08-14 15:02:44.925][INFO][607300][t27e37y5] NACK: update seq=26595, nack range [26594, 26595]
[2025-08-14 15:02:45.260][INFO][607300][t27e37y5] NACK: update seq=26668, nack range [26667, 26668]
[2025-08-14 15:02:45.565][INFO][607300][t27e37y5] NACK: update seq=26741, nack range [26740, 26741]
[2025-08-14 15:02:45.884][INFO][607300][t27e37y5] NACK: update seq=26816, nack range [26815, 26816]
[2025-08-14 15:02:46.255][INFO][607300][t27e37y5] NACK: update seq=26899, nack range [26898, 26899]
[2025-08-14 15:02:46.958][INFO][607300][t27e37y5] NACK: update seq=27061, nack range [27060, 27061]
[2025-08-14 15:02:47.128][INFO][607300][68l1dw2j] Hybrid cpu=5.00%,98MB, cid=2,6, timer=61,10,48, clock=0,44,3,0,0,0,0,0,0, objs=(pkt:513,raw:49,fua:463,msg:590,oth:1,buf:281)
[2025-08-14 15:02:47.128][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(287,rtp:281,stun:1,rtcp:5), spkts=(14,rtp:0,stun:1,rtcp:27), rtcp=(pli:1,twcc:9,rr:1), snk=(97,a:48,v:48,h:0), fid=(id:0,fid:287,ffid:0,addr:1,faddr:287)
[2025-08-14 15:02:47.635][INFO][607300][57o84c06] Process: cpu=6.00%,99MB, threads=2
[2025-08-14 15:02:47.949][INFO][607300][t27e37y5] NACK: update seq=27287, nack range [27286, 27287]
[2025-08-14 15:02:47.981][INFO][607300][80c8ibap] RTC: to rtmp bridge request key frame, ssrc=4106216783, publisher cid=t27e37y5
[2025-08-14 15:02:48.016][INFO][607300][t27e37y5] 35B video sh, codec(7, profile=Baseline, level=3.2, 960x540, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:02:48.016][INFO][607300][t27e37y5] set ts=1174206458, header=27299, lost=27300
[2025-08-14 15:02:48.848][INFO][607300][t27e37y5] NACK: update seq=27489, nack range [27488, 27489]
[2025-08-14 15:02:49.247][INFO][607300][t27e37y5] DTLS: After done, got 39 bytes
[2025-08-14 15:02:49.248][INFO][607300][t27e37y5] DTLS: State Passive RECV, done=1, arq=0, r0=39, len=39, cnt=21, size=26, hs=0
[33m[2025-08-14 15:02:49.248][WARN][607300][t27e37y5][0] DTLS: SSL3 alert method=read type=warning, desc=CN(close notify), where=16388, ret=256, r1=0
[0m[2025-08-14 15:02:49.248][INFO][607300][t27e37y5] RTC: session destroy by DTLS alert(warning CN), username=07ilz84n:EQuT
[2025-08-14 15:02:49.248][INFO][607300][t27e37y5] RTC: before dispose resource(RtcConn)(0x61d000010480), conns=1, zombies=0, ign=0, inz=0, ind=0
[2025-08-14 15:02:49.248][INFO][607300][t27e37y5] RTC: session detach from [t27e37y5](RtcConn), disposing=1
[2025-08-14 15:02:49.248][INFO][607300][i036lpvl] RTC: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:02:49.248][INFO][607300][t27e37y5] RTC: disposing #0 resource(RtcConn)(0x61d000010480), conns=1, disposing=1, zombies=0
[2025-08-14 15:02:49.248][INFO][607300][t27e37y5] cleanup when unpublish, created=1, deliver=1
[2025-08-14 15:02:49.248][INFO][607300][t27e37y5] Qavg: 205.527
[33m[2025-08-14 15:02:49.248][WARN][607300][t27e37y5][11] 2 frames left in the queue on closing
[0m[2025-08-14 15:02:49.248][INFO][607300][t27e37y5] cleanup when unpublish
[2025-08-14 15:02:50.369][INFO][607300][5k5xl543] Live: cleanup die source, id=[t27e37y5], total=1
[2025-08-14 15:02:50.379][INFO][607300][08l868c2] RTC: cleanup die source, id=[t27e37y5], total=1
[2025-08-14 15:02:52.128][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,102MB, cid=2,6, timer=61,10,48, clock=0,44,3,0,0,0,0,0,0, objs=(pkt:513,raw:49,fua:463,msg:590,oth:1,buf:281)
[2025-08-14 15:02:52.640][INFO][607300][57o84c06] Process: cpu=0.00%,102MB, threads=2
[2025-08-14 15:02:57.129][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,102MB, cid=2,6, timer=61,10,48, clock=0,44,3,0,0,0,0,0,0, objs=(pkt:513,raw:49,fua:463,msg:590,oth:1,buf:281)
[2025-08-14 15:02:57.644][INFO][607300][57o84c06] Process: cpu=0.00%,102MB, threads=2
[2025-08-14 15:03:02.130][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,102MB, cid=2,1, timer=62,1,6, clock=0,48,1,0,0,0,0,0,0, free=1, objs=(pkt:71,raw:7,fua:64,msg:82,oth:1,buf:39)
[2025-08-14 15:03:02.649][INFO][607300][57o84c06] Process: cpu=0.00%,102MB, threads=2
[2025-08-14 15:03:07.130][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,102MB, cid=2,1, timer=62,1,6, clock=0,48,1,0,0,0,0,0,0, free=1, objs=(pkt:71,raw:7,fua:64,msg:82,oth:1,buf:39)
[2025-08-14 15:03:07.655][INFO][607300][57o84c06] Process: cpu=0.00%,102MB, threads=2
[2025-08-14 15:03:12.130][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,103MB, cid=1,0, timer=63,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:03:12.660][INFO][607300][57o84c06] Process: cpu=0.00%,103MB, threads=2
[2025-08-14 15:03:17.131][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,103MB, cid=1,0, timer=63,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:03:17.230][INFO][607300][842m4665] HTTP #0 **************:54751 OPTIONS http://**************:1985/rtc/v1/publish/, content-length=-1
[2025-08-14 15:03:17.236][INFO][607300][842m4665] TCP: before dispose resource(HttpConn)(0x607000012540), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:03:17.236][WARN][607300][842m4665][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:03:17.236][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:03:17.236][INFO][607300][842m4665] TCP: disposing #0 resource(HttpConn)(0x607000012540), conns=1, disposing=1, zombies=0
[2025-08-14 15:03:17.241][INFO][607300][71ud9g12] HTTP #0 **************:54752 POST http://**************:1985/rtc/v1/publish/, content-length=6382
[2025-08-14 15:03:17.665][INFO][607300][57o84c06] Process: cpu=1.00%,103MB, threads=2
[2025-08-14 15:03:19.415][INFO][607300][71ud9g12] RTC publish webrtc://**************:8000/live/test0814, api=http://**************:1985/rtc/v1/publish/, tid=362a18b, clientip=**************, app=live, stream=test0814, offer=5878B, eip=, codec=
[2025-08-14 15:03:19.416][INFO][607300][71ud9g12] ignore attribute=, value=
[2025-08-14 15:03:19.418][INFO][607300][71ud9g12] new rtc source, stream_url=/live/test0814
[2025-08-14 15:03:19.419][INFO][607300][71ud9g12] RTC publisher nack=1, nnc=1, pt-drop=0, twcc=1/3
[2025-08-14 15:03:19.419][INFO][607300][71ud9g12] free rtc source id=[]
[2025-08-14 15:03:19.419][INFO][607300][71ud9g12] new live source, stream_url=/live/test0814
[2025-08-14 15:03:19.420][INFO][607300][71ud9g12] RTC: Init tracks {track: c47d3fb1-91b5-48c8-85ef-39523e81a092, is_active: 0=>1},{track: f6bc723e-cca1-4ad5-806b-e1e6a7b1f2f5, is_active: 0=>1}, ok
[2025-08-14 15:03:19.420][INFO][607300][71ud9g12] RTC: Use candidates **************, ************, protocol=udp
[2025-08-14 15:03:19.421][INFO][607300][71ud9g12] RTC init session, user=70997xz9:+lxD, url=/live/test0814, encrypt=1/1, DTLS(role=passive, version=auto), timeout=30000ms, nack=1
[2025-08-14 15:03:19.421][INFO][607300][71ud9g12] RTC username=70997xz9:+lxD, offer=5878B, answer=1605B
[2025-08-14 15:03:19.421][INFO][607300][71ud9g12] RTC remote offer: v=0\r\no=- 258971493718404632 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS 8380a886-dd2d-4006-8318-d327101b1d47\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:+lxD\r\na=ice-pwd:Flp+DRCpppZ/zxaW+X76PqrE\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D3:9D:EC:A7:E6:3B:19:68:1F:50:D3:27:56:7C:29:C2:6A:81:C6:D5:E2:0A:66:BD:67:93:65:EB:D0:51:95:4B\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:8380a886-dd2d-4006-8318-d327101b1d47 f6bc723e-cca1-4ad5-806b-e1e6a7b1f2f5\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 minptime=10;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:9 G722/8000\r\na=rtpmap:0 PCMU/8000\r\na=rtpmap:8 PCMA/8000\r\na=rtpmap:13 CN/8000\r\na=rtpmap:110 telephone-event/48000\r\na=rtpmap:126 telephone-event/8000\r\na=ssrc:1234415192 cname:nGE+mxUPnXpvrG/E\r\na=ssrc:1234415192 msid:8380a886-dd2d-4006-8318-d327101b1d47 f6bc723e-cca1-4ad5-806b-e1e6a7b1f2f5\r\nm=video 9 UDP/TLS/RTP/SAVPF 96 97 103 104 107 108 109 114 115 116 117 118 39 40 45 46 98 99 100 101 119 120 49 50 123 124 125\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:+lxD\r\na=ice-pwd:Flp+DRCpppZ/zxaW+X76PqrE\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D3:9D:EC:A7:E6:3B:19:68:1F:50:D3:27:56:7C:29:C2:6A:81:C6:D5:E2:0A:66:BD:67:93:65:EB:D0:51:95:4B\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:13 urn:3gpp:video-orientation\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendrecv\r\na=msid:8380a886-dd2d-4006-8318-d327101b1d47 c47d3fb1-91b5-48c8-85ef-39523e81a092\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:96 VP8/90000\r\na=rtcp-fb:96 goog-remb\r\na=rtcp-fb:96 transport-cc\r\na=rtcp-fb:96 ccm fir\r\na=rtcp-fb:96 nack\r\na=rtcp-fb:96 nack pli\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:103 H264/90000\r\na=rtcp-fb:103 goog-remb\r\na=rtcp-fb:103 transport-cc\r\na=rtcp-fb:103 ccm fir\r\na=rtcp-fb:103 nack\r\na=rtcp-fb:103 nack pli\r\na=fmtp:103 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f\r\na=rtpmap:104 rtx/90000\r\na=fmtp:104 apt=103\r\na=rtpmap:107 H264/90000\r\na=rtcp-fb:107 goog-remb\r\na=rtcp-fb:107 transport-cc\r\na=rtcp-fb:107 ccm fir\r\na=rtcp-fb:107 nack\r\na=rtcp-fb:107 nack pli\r\na=fmtp:107 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42001f\r\na=rtpmap:108 rtx/90000\r\na=fmtp:108 apt=107\r\na=rtpmap:109 H264/90000\r\na=rtcp-fb:109 goog-remb\r\na=rtcp-fb:109 transport-cc\r\na=rtcp-fb:109 ccm fir\r\na=rtcp-fb:109 nack\r\na=rtcp-fb:109 nack pli\r\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\r\na=rtpmap:114 rtx/90000\r\na=fmtp:114 apt=109\r\na=rtpmap:115 H264/90000\r\na=rtcp-fb:115 goog-remb\r\na=rtcp-fb:115 transport-cc\r\na=rtcp-fb:115 ccm fir\r\na=rtcp-fb:115 nack\r\na=rtcp-fb:115 nack pli\r\na=fmtp:115 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f\r\na=rtpmap:116 rtx/90000\r\na=fmtp:116 apt=115\r\na=rtpmap:117 H264/90000\r\na=rtcp-fb:117 goog-remb\r\na=rtcp-fb:117 transport-cc\r\na=rtcp-fb:117 ccm fir\r\na=rtcp-fb:117 nack\r\na=rtcp-fb:117 nack pli\r\na=fmtp:117 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\r\na=rtpmap:118 rtx/90000\r\na=fmtp:118 apt=117\r\na=rtpmap:39 H264/90000\r\na=rtcp-fb:39 goog-remb\r\na=rtcp-fb:39 transport-cc\r\na=rtcp-fb:39 ccm fir\r\na=rtcp-fb:39 nack\r\na=rtcp-fb:39 nack pli\r\na=fmtp:39 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=4d001f\r\na=rtpmap:40 rtx/90000\r\na=fmtp:40 apt=39\r\na=rtpmap:45 AV1/90000\r\na=rtcp-fb:45 goog-remb\r\na=rtcp-fb:45 transport-cc\r\na=rtcp-fb:45 ccm fir\r\na=rtcp-fb:45 nack\r\na=rtcp-fb:45 nack pli\r\na=fmtp:45 level-idx=5;profile=0;tier=0\r\na=rtpmap:46 rtx/90000\r\na=fmtp:46 apt=45\r\na=rtpmap:98 VP9/90000\r\na=rtcp-fb:98 goog-remb\r\na=rtcp-fb:98 transport-cc\r\na=rtcp-fb:98 ccm fir\r\na=rtcp-fb:98 nack\r\na=rtcp-fb:98 nack pli\r\na=fmtp:98 profile-id=0\r\na=rtpmap:99 rtx/90000\r\na=fmtp:99 apt=98\r\na=rtpmap:100 VP9/90000\r\na=rtcp-fb:100 goog-remb\r\na=rtcp-fb:100 transport-cc\r\na=rtcp-fb:100 ccm fir\r\na=rtcp-fb:100 nack\r\na=rtcp-fb:100 nack pli\r\na=fmtp:100 profile-id=2\r\na=rtpmap:101 rtx/90000\r\na=fmtp:101 apt=100\r\na=rtpmap:119 H264/90000\r\na=rtcp-fb:119 goog-remb\r\na=rtcp-fb:119 transport-cc\r\na=rtcp-fb:119 ccm fir\r\na=rtcp-fb:119 nack\r\na=rtcp-fb:119 nack pli\r\na=fmtp:119 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=64001f\r\na=rtpmap:120 rtx/90000\r\na=fmtp:120 apt=119\r\na=rtpmap:49 H265/90000\r\na=rtcp-fb:49 goog-remb\r\na=rtcp-fb:49 transport-cc\r\na=rtcp-fb:49 ccm fir\r\na=rtcp-fb:49 nack\r\na=rtcp-fb:49 nack pli\r\na=fmtp:49 level-id=93;profile-id=1;tier-flag=0;tx-mode=SRST\r\na=rtpmap:50 rtx/90000\r\na=fmtp:50 apt=49\r\na=rtpmap:123 red/90000\r\na=rtpmap:124 rtx/90000\r\na=fmtp:124 apt=123\r\na=rtpmap:125 ulpfec/90000\r\na=ssrc-group:FID 1182706269 2685424189\r\na=ssrc:1182706269 cname:nGE+mxUPnXpvrG/E\r\na=ssrc:1182706269 msid:8380a886-dd2d-4006-8318-d327101b1d47 c47d3fb1-91b5-48c8-85ef-39523e81a092\r\na=ssrc:2685424189 cname:nGE+mxUPnXpvrG/E\r\na=ssrc:2685424189 msid:8380a886-dd2d-4006-8318-d327101b1d47 c47d3fb1-91b5-48c8-85ef-39523e81a092\r\n
[2025-08-14 15:03:19.422][INFO][607300][71ud9g12] RTC local answer: v=0\r\no=SRS/6.0.134(Hang) 107545981202048 2 IN IP4 0.0.0.0\r\ns=SRSPublishSession\r\nt=0 0\r\na=ice-lite\r\na=group:BUNDLE 0 1\r\na=msid-semantic: WMS live/test0814\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:70997xz9\r\na=ice-pwd:uwyf2049s421530069wqw456hn65j0me\r\na=fingerprint:sha-256 66:CF:6E:3E:8F:63:77:AE:D0:C0:67:B0:E7:4B:01:45:0D:4E:38:C2:74:09:FF:BE:D0:4C:06:1B:06:17:55:67\r\na=setup:passive\r\na=mid:0\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 minptime=10;useinbandfec=1\r\na=candidate:0 1 udp 2130706431 ************** 8000 typ host generation 0\r\na=candidate:1 1 udp 2130706431 ************ 8000 typ host generation 0\r\nm=video 9 UDP/TLS/RTP/SAVPF 109 123\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:70997xz9\r\na=ice-pwd:uwyf2049s421530069wqw456hn65j0me\r\na=fingerprint:sha-256 66:CF:6E:3E:8F:63:77:AE:D0:C0:67:B0:E7:4B:01:45:0D:4E:38:C2:74:09:FF:BE:D0:4C:06:1B:06:17:55:67\r\na=setup:passive\r\na=mid:1\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:109 H264/90000\r\na=rtcp-fb:109 transport-cc\r\na=rtcp-fb:109 nack\r\na=rtcp-fb:109 nack pli\r\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\r\na=rtpmap:123 red/90000\r\na=candidate:0 1 udp 2130706431 ************** 8000 typ host generation 0\r\na=candidate:1 1 udp 2130706431 ************ 8000 typ host generation 0\r\n
[2025-08-14 15:03:19.428][INFO][607300][71ud9g12] TCP: before dispose resource(HttpConn)(0x6070000127e0), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:03:19.428][WARN][607300][71ud9g12][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:03:19.428][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:03:19.428][INFO][607300][71ud9g12] TCP: disposing #0 resource(HttpConn)(0x6070000127e0), conns=1, disposing=1, zombies=0
[2025-08-14 15:03:19.435][INFO][607300][71ud9g12] RTC: session address init **************:54408
[2025-08-14 15:03:19.435][INFO][607300][71ud9g12] RTC: session STUN done, waiting DTLS handshake.
[2025-08-14 15:03:19.435][INFO][607300][r928i43c] <- RTC RECV #12, udp 2114, pps 16/56, schedule 2114
[2025-08-14 15:03:19.439][INFO][607300][71ud9g12] DTLS: State Passive RECV, done=0, arq=0, r0=153, len=153, cnt=22, size=140, hs=1
[2025-08-14 15:03:19.439][INFO][607300][71ud9g12] DTLS: State Passive SEND, done=0, arq=0, r0=0, len=706, cnt=22, size=110, hs=2
[2025-08-14 15:03:19.443][INFO][607300][71ud9g12] DTLS: State Passive RECV, done=0, arq=0, r0=578, len=578, cnt=22, size=298, hs=11
[2025-08-14 15:03:19.444][INFO][607300][71ud9g12] DTLS: State Passive SEND, done=0, arq=0, r0=0, len=75, cnt=20, size=1, hs=1
[2025-08-14 15:03:19.444][INFO][607300][71ud9g12] RTC: DTLS handshake done.
[2025-08-14 15:03:19.444][INFO][607300][71ud9g12] RTC: session pub=1, sub=0, to=30000ms connection established
[2025-08-14 15:03:19.444][INFO][607300][71ud9g12] RTC: Publisher url=/live/test0814 established
[2025-08-14 15:03:19.447][INFO][607300][71ud9g12] ignore disabled exec for vhost=__defaultVhost__
[33m[2025-08-14 15:03:19.448][WARN][607300][r928i43c][22] handle udp pkt, count=1/1, err: code=4001(HttpPatternDuplicated)(Failed to handle HTTP request for pattern duplicated) : size=578, data=[16 fe fd 00 00 00 00 00] : on_dtls size=578, data=[16 fe fd 00 00 00 00 00 00 00 01 01 2a 0b 00 01 1e 00 01 00 00 00 00 01 1e 00 01 1b 00 01 18 30] : done : dtls done : udp : start publish : on publish : bridge on publish : source publish : handle publish : http mount : http: mount flv stream for vhost=/live/test0814 failed : pattern=/live/test0814.flv exists
thread [607300][r928i43c]: cycle() [./src/app/srs_app_listener.cpp:743][errno=22]
thread [607300][71ud9g12]: on_dtls() [./src/app/srs_app_rtc_dtls.cpp:564][errno=22]
thread [607300][71ud9g12]: do_on_dtls() [./src/app/srs_app_rtc_dtls.cpp:615][errno=22]
thread [607300][71ud9g12]: on_handshake_done() [./src/app/srs_app_rtc_dtls.cpp:852][errno=22]
thread [607300][71ud9g12]: on_dtls_handshake_done() [./src/app/srs_app_rtc_network.cpp:219][errno=22]
thread [607300][71ud9g12]: on_dtls_handshake_done() [./src/app/srs_app_rtc_conn.cpp:2237][errno=22]
thread [607300][71ud9g12]: start() [./src/app/srs_app_rtc_conn.cpp:1262][errno=22]
thread [607300][71ud9g12]: on_publish() [./src/app/srs_app_rtc_source.cpp:639][errno=22]
thread [607300][71ud9g12]: on_publish() [./src/app/srs_app_stream_bridge.cpp:48][errno=22]
thread [607300][71ud9g12]: on_publish() [./src/app/srs_app_source.cpp:2598][errno=22]
thread [607300][71ud9g12]: on_publish() [./src/app/srs_app_server.cpp:1321][errno=22]
thread [607300][71ud9g12]: http_mount() [./src/app/srs_app_http_stream.cpp:1033][errno=22]
thread [607300][71ud9g12]: handle() [./src/protocol/srs_protocol_http_stack.cpp:700][errno=22]
[0m[2025-08-14 15:03:19.461][INFO][607300][r928i43c] RTC: Discard no-sync Audio, ssrc=1234415192, seq=5057, ts=1952130392, state=-1
[2025-08-14 15:03:21.162][INFO][607300][71ud9g12] RTC: Accept sync Video, ssrc=1182706269, seq=4328, ts=1990128485, state=0
[2025-08-14 15:03:22.132][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,106MB, cid=1,0, timer=63,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:03:22.132][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(24,rtp:23,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:1), rtcp=(pli:1,twcc:1,rr:1), snk=(13,a:6,v:6,h:0), fid=(id:1,fid:24,ffid:0,addr:1,faddr:24)
[2025-08-14 15:03:22.670][INFO][607300][57o84c06] Process: cpu=1.00%,106MB, threads=2
[2025-08-14 15:03:22.775][INFO][607300][97w33200] HTTP #0 **************:40826 GET http://**************:1985/api/v1/streams/, content-length=-1
[2025-08-14 15:03:22.776][INFO][607300][97w33200] TCP: before dispose resource(HttpConn)(0x607000030370), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:03:22.776][WARN][607300][97w33200][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:03:22.776][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:03:22.776][INFO][607300][97w33200] TCP: disposing #0 resource(HttpConn)(0x607000030370), conns=1, disposing=1, zombies=0
[2025-08-14 15:03:24.701][INFO][607300][71ud9g12] 7B audio sh, codec(10, profile=LC, 2channels, 0kbps, 48000HZ), flv(16bits, 2channels, 44100HZ)
[2025-08-14 15:03:24.721][INFO][607300][71ud9g12] time diff to large=4096790630, next out=4183956368, new pkt=4183956388, set to new pkt
[2025-08-14 15:03:27.132][INFO][607300][68l1dw2j] Hybrid cpu=4.00%,108MB, cid=6,2, timer=62,5,25, clock=0,46,2,0,0,0,0,0,0, free=1, objs=(pkt:54,raw:52,fua:1,msg:62,oth:1,buf:42)
[2025-08-14 15:03:27.132][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(24,rtp:23,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:1), rtcp=(pli:1,twcc:1,rr:1), snk=(13,a:6,v:6,h:0), fid=(id:1,fid:24,ffid:0,addr:1,faddr:24)
[2025-08-14 15:03:27.675][INFO][607300][57o84c06] Process: cpu=4.00%,109MB, threads=2
[2025-08-14 15:03:29.434][INFO][607300][r928i43c] <- RTC RECV #12, udp 833, pps 17/83, schedule 833
[2025-08-14 15:03:31.623][INFO][607300][71ud9g12] NACK: update seq=5663, nack range [5662, 5663]
[2025-08-14 15:03:31.626][INFO][607300][71ud9g12] NACK: update seq=5666, nack range [5665, 5666]
[2025-08-14 15:03:31.628][INFO][607300][71ud9g12] NACK: update seq=4644, nack range [4643, 4644]
[2025-08-14 15:03:31.971][INFO][607300][6a673kk6][MAYBE] RTC: NACK timeout=1, request PLI, track=c47d3fb1-91b5-48c8-85ef-39523e81a092, ssrc=1182706269
[2025-08-14 15:03:32.132][INFO][607300][68l1dw2j] Hybrid cpu=4.00%,111MB, cid=6,2, timer=62,5,25, clock=0,46,2,0,0,0,0,0,0, free=1, objs=(pkt:54,raw:52,fua:1,msg:62,oth:1,buf:42)
[2025-08-14 15:03:32.132][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(24,rtp:23,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:1), rtcp=(pli:1,twcc:1,rr:1), snk=(13,a:6,v:6,h:0), fid=(id:1,fid:24,ffid:0,addr:1,faddr:24)
[2025-08-14 15:03:32.680][INFO][607300][57o84c06] Process: cpu=4.00%,111MB, threads=2
[2025-08-14 15:03:37.133][INFO][607300][68l1dw2j] Hybrid cpu=4.00%,114MB, cid=6,2, timer=62,5,25, clock=0,46,2,0,0,0,0,0,0, free=1, objs=(pkt:54,raw:52,fua:1,msg:62,oth:1,buf:42)
[2025-08-14 15:03:37.133][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(79,rtp:77,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:4), snk=(97,a:48,v:48,h:2), fid=(id:0,fid:79,ffid:0,addr:1,faddr:79)
[2025-08-14 15:03:37.686][INFO][607300][57o84c06] Process: cpu=4.00%,114MB, threads=2
[2025-08-14 15:03:39.450][INFO][607300][r928i43c] <- RTC RECV #12, udp 756, pps 18/75, schedule 756
[2025-08-14 15:03:42.133][INFO][607300][68l1dw2j] Hybrid cpu=4.00%,117MB, cid=1,1, timer=61,10,48, clock=0,44,3,0,0,0,0,0,0, objs=(pkt:101,raw:101,fua:0,msg:148,oth:0,buf:75)
[2025-08-14 15:03:42.134][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(79,rtp:77,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:4), snk=(97,a:48,v:48,h:2), fid=(id:0,fid:79,ffid:0,addr:1,faddr:79)
[2025-08-14 15:03:42.691][INFO][607300][57o84c06] Process: cpu=4.00%,117MB, threads=2
[2025-08-14 15:03:47.134][INFO][607300][68l1dw2j] Hybrid cpu=4.00%,120MB, cid=1,1, timer=61,10,48, clock=0,44,3,0,0,0,0,0,0, objs=(pkt:101,raw:101,fua:0,msg:148,oth:0,buf:75)
[2025-08-14 15:03:47.134][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(79,rtp:77,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:4), snk=(97,a:48,v:48,h:2), fid=(id:0,fid:79,ffid:0,addr:1,faddr:79)
[2025-08-14 15:03:47.696][INFO][607300][57o84c06] Process: cpu=4.00%,120MB, threads=2
[2025-08-14 15:03:49.440][INFO][607300][r928i43c] <- RTC RECV #12, udp 805, pps 19/80, schedule 805
[2025-08-14 15:03:52.135][INFO][607300][68l1dw2j] Hybrid cpu=4.00%,123MB, cid=1,1, timer=61,10,48, clock=0,44,3,0,0,0,0,0,0, objs=(pkt:101,raw:101,fua:0,msg:148,oth:0,buf:75)
[2025-08-14 15:03:52.135][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(78,rtp:77,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:0), snk=(97,a:48,v:48,h:0), fid=(id:0,fid:78,ffid:0,addr:1,faddr:78)
[2025-08-14 15:03:52.701][INFO][607300][57o84c06] Process: cpu=4.00%,123MB, threads=2
[2025-08-14 15:03:57.136][INFO][607300][68l1dw2j] Hybrid cpu=4.00%,126MB, cid=1,1, timer=62,10,48, clock=0,45,3,0,0,0,0,0,0, objs=(pkt:106,raw:106,fua:0,msg:153,oth:0,buf:78)
[2025-08-14 15:03:57.136][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(78,rtp:77,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:0), snk=(97,a:48,v:48,h:0), fid=(id:0,fid:78,ffid:0,addr:1,faddr:78)
[2025-08-14 15:03:57.706][INFO][607300][57o84c06] Process: cpu=4.00%,126MB, threads=2
[2025-08-14 15:03:58.001][INFO][607300][yzj92dg2] HTTP #0 **************:55394 GET http://**************:1985/api/v1/summaries, content-length=-1
[2025-08-14 15:03:58.055][INFO][607300][yzj92dg2] TCP: before dispose resource(HttpConn)(0x6070000307d0), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:03:58.055][WARN][607300][yzj92dg2][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:03:58.055][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:03:58.055][INFO][607300][yzj92dg2] TCP: disposing #0 resource(HttpConn)(0x6070000307d0), conns=1, disposing=1, zombies=0
[2025-08-14 15:03:59.462][INFO][607300][r928i43c] <- RTC RECV #12, udp 795, pps 20/79, schedule 795
[2025-08-14 15:04:02.136][INFO][607300][68l1dw2j] Hybrid cpu=4.00%,129MB, cid=1,1, timer=62,10,48, clock=0,45,3,0,0,0,0,0,0, objs=(pkt:106,raw:106,fua:0,msg:153,oth:0,buf:78)
[2025-08-14 15:04:02.136][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(77,rtp:76,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:0), snk=(97,a:48,v:48,h:0), fid=(id:0,fid:77,ffid:0,addr:1,faddr:77)
[2025-08-14 15:04:02.712][INFO][607300][57o84c06] Process: cpu=5.00%,129MB, threads=2
[2025-08-14 15:04:07.137][INFO][607300][68l1dw2j] Hybrid cpu=4.00%,132MB, cid=1,1, timer=62,10,48, clock=0,45,3,0,0,0,0,0,0, objs=(pkt:106,raw:106,fua:0,msg:153,oth:0,buf:78)
[2025-08-14 15:04:07.137][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(77,rtp:76,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:0), snk=(97,a:48,v:48,h:0), fid=(id:0,fid:77,ffid:0,addr:1,faddr:77)
[2025-08-14 15:04:07.717][INFO][607300][57o84c06] Process: cpu=4.00%,132MB, threads=2
[2025-08-14 15:04:09.375][INFO][607300][71ud9g12] DTLS: After done, got 39 bytes
[2025-08-14 15:04:09.375][INFO][607300][71ud9g12] DTLS: State Passive RECV, done=1, arq=0, r0=39, len=39, cnt=21, size=26, hs=0
[33m[2025-08-14 15:04:09.375][WARN][607300][71ud9g12][0] DTLS: SSL3 alert method=read type=warning, desc=CN(close notify), where=16388, ret=256, r1=0
[0m[2025-08-14 15:04:09.375][INFO][607300][71ud9g12] RTC: session destroy by DTLS alert(warning CN), username=70997xz9:+lxD
[2025-08-14 15:04:09.375][INFO][607300][71ud9g12] RTC: before dispose resource(RtcConn)(0x61d00001ae80), conns=1, zombies=0, ign=0, inz=0, ind=0
[2025-08-14 15:04:09.375][INFO][607300][71ud9g12] RTC: session detach from [71ud9g12](RtcConn), disposing=1
[2025-08-14 15:04:12.137][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,133MB, cid=2,2, timer=62,11,48, clock=0,45,3,0,0,0,0,0,0, free=1, objs=(pkt:79,raw:79,fua:0,msg:117,oth:0,buf:60)
[2025-08-14 15:04:12.722][INFO][607300][57o84c06] Process: cpu=1.00%,133MB, threads=2
[2025-08-14 15:04:14.304][INFO][607300][65f9481m] HTTP #0 **************:55398 OPTIONS http://**************:1985/rtc/v1/publish/, content-length=-1
[2025-08-14 15:04:14.308][INFO][607300][65f9481m] TCP: before dispose resource(HttpConn)(0x607000030bc0), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:04:14.308][WARN][607300][65f9481m][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:04:14.308][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:04:14.308][INFO][607300][65f9481m] TCP: disposing #0 resource(HttpConn)(0x607000030bc0), conns=1, disposing=1, zombies=0
[2025-08-14 15:04:14.312][INFO][607300][f3x56k5v] HTTP #0 **************:55399 POST http://**************:1985/rtc/v1/publish/, content-length=6383
[2025-08-14 15:04:16.483][INFO][607300][f3x56k5v] RTC publish webrtc://**************:8000/live/test0814, api=http://**************:1985/rtc/v1/publish/, tid=1a3fbb5, clientip=**************, app=live, stream=test0814, offer=5879B, eip=, codec=
[2025-08-14 15:04:16.483][INFO][607300][f3x56k5v] ignore attribute=, value=
[33m[2025-08-14 15:04:16.485][WARN][607300][f3x56k5v][11] RTC error code=5020(RtcStreamBusy)(RTC stream already exists or busy) : serve : create session : stream /live/test0814 busy
thread [607300][f3x56k5v]: do_serve_http() [./src/app/srs_app_rtc_api.cpp:466][errno=11]
thread [607300][f3x56k5v]: serve_http() [./src/app/srs_app_rtc_api.cpp:511][errno=11]
thread [607300][f3x56k5v]: create_session() [./src/app/srs_app_rtc_server.cpp:511][errno=11]
[0m[2025-08-14 15:04:16.532][INFO][607300][f3x56k5v] TCP: before dispose resource(HttpConn)(0x607000030e60), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:04:16.533][WARN][607300][f3x56k5v][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:04:16.533][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:04:16.533][INFO][607300][f3x56k5v] TCP: disposing #0 resource(HttpConn)(0x607000030e60), conns=1, disposing=1, zombies=0
[2025-08-14 15:04:17.137][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,134MB, cid=2,2, timer=62,11,48, clock=0,45,3,0,0,0,0,0,0, free=1, objs=(pkt:79,raw:79,fua:0,msg:117,oth:0,buf:60)
[2025-08-14 15:04:17.727][INFO][607300][57o84c06] Process: cpu=0.00%,134MB, threads=2
[2025-08-14 15:04:22.138][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,134MB, cid=2,2, timer=62,11,48, clock=0,45,3,0,0,0,0,0,0, free=1, objs=(pkt:79,raw:79,fua:0,msg:117,oth:0,buf:60)
[2025-08-14 15:04:22.732][INFO][607300][57o84c06] Process: cpu=0.00%,134MB, threads=2
[2025-08-14 15:04:27.138][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,134MB, cid=2,1, timer=62,10,49, clock=0,48,1,0,0,0,0,0,0, free=1
[2025-08-14 15:04:27.737][INFO][607300][57o84c06] Process: cpu=0.00%,134MB, threads=2
[2025-08-14 15:04:32.138][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,134MB, cid=2,1, timer=62,10,49, clock=0,48,1,0,0,0,0,0,0, free=1
[2025-08-14 15:04:32.218][INFO][607300][t27e37y5] http: unmount flv stream for sid=/live/test0814, i=1024
[2025-08-14 15:04:32.218][INFO][607300][t27e37y5] free live source id=[t27e37y5]
[2025-08-14 15:04:32.220][INFO][607300][t27e37y5] free rtc source id=[t27e37y5]
[33m[2025-08-14 15:04:32.221][WARN][607300][t27e37y5][4][DTLS_HANG] DTLS: Hang, done=0, version=-1, arq=0
[0m[2025-08-14 15:04:32.221][INFO][607300][i036lpvl] RTC: clear zombies=1 resources, conns=1, removing=0, unsubs=1
[2025-08-14 15:04:32.221][INFO][607300][71ud9g12] RTC: disposing #0 resource(RtcConn)(0x61d00001ae80), conns=1, disposing=1, zombies=0
[2025-08-14 15:04:32.221][INFO][607300][71ud9g12] cleanup when unpublish, created=1, deliver=1
[2025-08-14 15:04:32.221][INFO][607300][71ud9g12] Qavg: 203.619
[33m[2025-08-14 15:04:32.221][WARN][607300][71ud9g12][4] 2 frames left in the queue on closing
[0m[2025-08-14 15:04:32.222][INFO][607300][71ud9g12] cleanup when unpublish
[2025-08-14 15:04:32.222][INFO][607300][71ud9g12] http: unmount flv stream for sid=/live/test0814, i=0
[33m[2025-08-14 15:04:32.228][WARN][607300][71ud9g12][4][DTLS_HANG] DTLS: Hang, done=0, version=-1, arq=0
[0m[2025-08-14 15:04:32.742][INFO][607300][57o84c06] Process: cpu=2.00%,134MB, threads=2
[2025-08-14 15:04:35.426][INFO][607300][5k5xl543] Live: cleanup die source, id=[71ud9g12], total=1
[2025-08-14 15:04:35.427][INFO][607300][5k5xl543] free live source id=[71ud9g12]
[2025-08-14 15:04:35.440][INFO][607300][08l868c2] RTC: cleanup die source, id=[71ud9g12], total=1
[2025-08-14 15:04:35.441][INFO][607300][08l868c2] free rtc source id=[71ud9g12]
[2025-08-14 15:04:37.139][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,134MB, cid=2,1, timer=62,10,49, clock=0,48,1,0,0,0,0,0,0, free=1
[2025-08-14 15:04:37.747][INFO][607300][57o84c06] Process: cpu=0.00%,134MB, threads=2
[2025-08-14 15:04:42.139][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,134MB, cid=2,1, timer=62,3,16, clock=0,49,0,1,0,0,0,0,0, free=1, objs=(pkt:0,raw:0,fua:0,msg:1,oth:0,buf:0)
[2025-08-14 15:04:42.752][INFO][607300][57o84c06] Process: cpu=0.00%,134MB, threads=2
[2025-08-14 15:04:47.140][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,134MB, cid=2,1, timer=62,3,16, clock=0,49,0,1,0,0,0,0,0, free=1, objs=(pkt:0,raw:0,fua:0,msg:1,oth:0,buf:0)
[2025-08-14 15:04:47.757][INFO][607300][57o84c06] Process: cpu=0.00%,134MB, threads=2
[2025-08-14 15:04:52.141][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,135MB, cid=2,1, timer=62,3,16, clock=0,49,0,1,0,0,0,0,0, free=1, objs=(pkt:0,raw:0,fua:0,msg:1,oth:0,buf:0)
[2025-08-14 15:04:52.762][INFO][607300][57o84c06] Process: cpu=1.00%,135MB, threads=2
[2025-08-14 15:04:57.142][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,135MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:04:57.767][INFO][607300][57o84c06] Process: cpu=0.00%,135MB, threads=2
[2025-08-14 15:05:02.142][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,135MB, cid=1,0, timer=62,0,0, clock=0,49,1,0,0,0,0,0,0
[2025-08-14 15:05:02.772][INFO][607300][57o84c06] Process: cpu=0.00%,135MB, threads=2
[2025-08-14 15:05:07.143][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,135MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:05:07.778][INFO][607300][57o84c06] Process: cpu=0.00%,135MB, threads=2
[2025-08-14 15:05:12.143][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,135MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:05:12.783][INFO][607300][57o84c06] Process: cpu=1.00%,135MB, threads=2
[2025-08-14 15:05:17.143][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,135MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:05:17.788][INFO][607300][57o84c06] Process: cpu=0.00%,135MB, threads=2
[2025-08-14 15:05:21.032][INFO][607300][71ea98q8] HTTP #0 **************:51204 OPTIONS http://**************:1985/rtc/v1/publish/, content-length=-1
[2025-08-14 15:05:21.036][INFO][607300][71ea98q8] TCP: before dispose resource(HttpConn)(0x607000034350), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:05:21.036][WARN][607300][71ea98q8][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:05:21.036][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:05:21.036][INFO][607300][71ea98q8] TCP: disposing #0 resource(HttpConn)(0x607000034350), conns=1, disposing=1, zombies=0
[2025-08-14 15:05:21.042][INFO][607300][4qh6jde6] HTTP #0 **************:51205 POST http://**************:1985/rtc/v1/publish/, content-length=6378
[2025-08-14 15:05:22.143][INFO][607300][68l1dw2j] Hybrid cpu=0.00%,135MB, cid=1,0, timer=62,0,0, clock=0,49,0,0,0,0,0,0,0
[2025-08-14 15:05:22.793][INFO][607300][57o84c06] Process: cpu=0.00%,135MB, threads=2
[2025-08-14 15:05:23.207][INFO][607300][4qh6jde6] RTC publish webrtc://**************:8000/live/test0814, api=http://**************:1985/rtc/v1/publish/, tid=6f971bb, clientip=**************, app=live, stream=test0814, offer=5874B, eip=, codec=
[2025-08-14 15:05:23.207][INFO][607300][4qh6jde6] ignore attribute=, value=
[2025-08-14 15:05:23.209][INFO][607300][4qh6jde6] new rtc source, stream_url=/live/test0814
[2025-08-14 15:05:23.210][INFO][607300][4qh6jde6] RTC publisher nack=1, nnc=1, pt-drop=0, twcc=1/3
[2025-08-14 15:05:23.211][INFO][607300][4qh6jde6] free rtc source id=[]
[2025-08-14 15:05:23.211][INFO][607300][4qh6jde6] new live source, stream_url=/live/test0814
[2025-08-14 15:05:23.211][INFO][607300][4qh6jde6] RTC: Init tracks {track: 63a080d5-c257-4b24-a5f1-9f5d5007d675, is_active: 0=>1},{track: 34397033-a2c7-41f6-957a-2b78a47e5b5b, is_active: 0=>1}, ok
[2025-08-14 15:05:23.212][INFO][607300][4qh6jde6] RTC: Use candidates **************, ************, protocol=udp
[2025-08-14 15:05:23.213][INFO][607300][4qh6jde6] RTC init session, user=a9a86s2w:wQe+, url=/live/test0814, encrypt=1/1, DTLS(role=passive, version=auto), timeout=30000ms, nack=1
[2025-08-14 15:05:23.213][INFO][607300][4qh6jde6] RTC username=a9a86s2w:wQe+, offer=5874B, answer=1605B
[2025-08-14 15:05:23.213][INFO][607300][4qh6jde6] RTC remote offer: v=0\r\no=- 8847192135678044231 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS b8ad18af-468f-4afd-ba4c-1f7a09566bd9\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:wQe+\r\na=ice-pwd:ssTwZiazIH9QNPsh/g5a5Uhi\r\na=ice-options:trickle\r\na=fingerprint:sha-256 64:E7:48:AF:DF:1B:A8:05:EA:00:F0:1D:3B:24:EE:35:6B:8E:55:F4:6F:EA:E8:8F:FC:EF:7D:A6:02:86:F8:D9\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:b8ad18af-468f-4afd-ba4c-1f7a09566bd9 34397033-a2c7-41f6-957a-2b78a47e5b5b\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 minptime=10;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:9 G722/8000\r\na=rtpmap:0 PCMU/8000\r\na=rtpmap:8 PCMA/8000\r\na=rtpmap:13 CN/8000\r\na=rtpmap:110 telephone-event/48000\r\na=rtpmap:126 telephone-event/8000\r\na=ssrc:973802362 cname:/DoebkgOqgFmeR6g\r\na=ssrc:973802362 msid:b8ad18af-468f-4afd-ba4c-1f7a09566bd9 34397033-a2c7-41f6-957a-2b78a47e5b5b\r\nm=video 9 UDP/TLS/RTP/SAVPF 96 97 103 104 107 108 109 114 115 116 117 118 39 40 45 46 98 99 100 101 119 120 49 50 123 124 125\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:wQe+\r\na=ice-pwd:ssTwZiazIH9QNPsh/g5a5Uhi\r\na=ice-options:trickle\r\na=fingerprint:sha-256 64:E7:48:AF:DF:1B:A8:05:EA:00:F0:1D:3B:24:EE:35:6B:8E:55:F4:6F:EA:E8:8F:FC:EF:7D:A6:02:86:F8:D9\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:13 urn:3gpp:video-orientation\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendrecv\r\na=msid:b8ad18af-468f-4afd-ba4c-1f7a09566bd9 63a080d5-c257-4b24-a5f1-9f5d5007d675\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:96 VP8/90000\r\na=rtcp-fb:96 goog-remb\r\na=rtcp-fb:96 transport-cc\r\na=rtcp-fb:96 ccm fir\r\na=rtcp-fb:96 nack\r\na=rtcp-fb:96 nack pli\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:103 H264/90000\r\na=rtcp-fb:103 goog-remb\r\na=rtcp-fb:103 transport-cc\r\na=rtcp-fb:103 ccm fir\r\na=rtcp-fb:103 nack\r\na=rtcp-fb:103 nack pli\r\na=fmtp:103 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f\r\na=rtpmap:104 rtx/90000\r\na=fmtp:104 apt=103\r\na=rtpmap:107 H264/90000\r\na=rtcp-fb:107 goog-remb\r\na=rtcp-fb:107 transport-cc\r\na=rtcp-fb:107 ccm fir\r\na=rtcp-fb:107 nack\r\na=rtcp-fb:107 nack pli\r\na=fmtp:107 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42001f\r\na=rtpmap:108 rtx/90000\r\na=fmtp:108 apt=107\r\na=rtpmap:109 H264/90000\r\na=rtcp-fb:109 goog-remb\r\na=rtcp-fb:109 transport-cc\r\na=rtcp-fb:109 ccm fir\r\na=rtcp-fb:109 nack\r\na=rtcp-fb:109 nack pli\r\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\r\na=rtpmap:114 rtx/90000\r\na=fmtp:114 apt=109\r\na=rtpmap:115 H264/90000\r\na=rtcp-fb:115 goog-remb\r\na=rtcp-fb:115 transport-cc\r\na=rtcp-fb:115 ccm fir\r\na=rtcp-fb:115 nack\r\na=rtcp-fb:115 nack pli\r\na=fmtp:115 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f\r\na=rtpmap:116 rtx/90000\r\na=fmtp:116 apt=115\r\na=rtpmap:117 H264/90000\r\na=rtcp-fb:117 goog-remb\r\na=rtcp-fb:117 transport-cc\r\na=rtcp-fb:117 ccm fir\r\na=rtcp-fb:117 nack\r\na=rtcp-fb:117 nack pli\r\na=fmtp:117 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\r\na=rtpmap:118 rtx/90000\r\na=fmtp:118 apt=117\r\na=rtpmap:39 H264/90000\r\na=rtcp-fb:39 goog-remb\r\na=rtcp-fb:39 transport-cc\r\na=rtcp-fb:39 ccm fir\r\na=rtcp-fb:39 nack\r\na=rtcp-fb:39 nack pli\r\na=fmtp:39 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=4d001f\r\na=rtpmap:40 rtx/90000\r\na=fmtp:40 apt=39\r\na=rtpmap:45 AV1/90000\r\na=rtcp-fb:45 goog-remb\r\na=rtcp-fb:45 transport-cc\r\na=rtcp-fb:45 ccm fir\r\na=rtcp-fb:45 nack\r\na=rtcp-fb:45 nack pli\r\na=fmtp:45 level-idx=5;profile=0;tier=0\r\na=rtpmap:46 rtx/90000\r\na=fmtp:46 apt=45\r\na=rtpmap:98 VP9/90000\r\na=rtcp-fb:98 goog-remb\r\na=rtcp-fb:98 transport-cc\r\na=rtcp-fb:98 ccm fir\r\na=rtcp-fb:98 nack\r\na=rtcp-fb:98 nack pli\r\na=fmtp:98 profile-id=0\r\na=rtpmap:99 rtx/90000\r\na=fmtp:99 apt=98\r\na=rtpmap:100 VP9/90000\r\na=rtcp-fb:100 goog-remb\r\na=rtcp-fb:100 transport-cc\r\na=rtcp-fb:100 ccm fir\r\na=rtcp-fb:100 nack\r\na=rtcp-fb:100 nack pli\r\na=fmtp:100 profile-id=2\r\na=rtpmap:101 rtx/90000\r\na=fmtp:101 apt=100\r\na=rtpmap:119 H264/90000\r\na=rtcp-fb:119 goog-remb\r\na=rtcp-fb:119 transport-cc\r\na=rtcp-fb:119 ccm fir\r\na=rtcp-fb:119 nack\r\na=rtcp-fb:119 nack pli\r\na=fmtp:119 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=64001f\r\na=rtpmap:120 rtx/90000\r\na=fmtp:120 apt=119\r\na=rtpmap:49 H265/90000\r\na=rtcp-fb:49 goog-remb\r\na=rtcp-fb:49 transport-cc\r\na=rtcp-fb:49 ccm fir\r\na=rtcp-fb:49 nack\r\na=rtcp-fb:49 nack pli\r\na=fmtp:49 level-id=93;profile-id=1;tier-flag=0;tx-mode=SRST\r\na=rtpmap:50 rtx/90000\r\na=fmtp:50 apt=49\r\na=rtpmap:123 red/90000\r\na=rtpmap:124 rtx/90000\r\na=fmtp:124 apt=123\r\na=rtpmap:125 ulpfec/90000\r\na=ssrc-group:FID 330064030 3075471361\r\na=ssrc:330064030 cname:/DoebkgOqgFmeR6g\r\na=ssrc:330064030 msid:b8ad18af-468f-4afd-ba4c-1f7a09566bd9 63a080d5-c257-4b24-a5f1-9f5d5007d675\r\na=ssrc:3075471361 cname:/DoebkgOqgFmeR6g\r\na=ssrc:3075471361 msid:b8ad18af-468f-4afd-ba4c-1f7a09566bd9 63a080d5-c257-4b24-a5f1-9f5d5007d675\r\n
[2025-08-14 15:05:23.213][INFO][607300][4qh6jde6] RTC local answer: v=0\r\no=SRS/6.0.134(Hang) 107545981243008 2 IN IP4 0.0.0.0\r\ns=SRSPublishSession\r\nt=0 0\r\na=ice-lite\r\na=group:BUNDLE 0 1\r\na=msid-semantic: WMS live/test0814\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:a9a86s2w\r\na=ice-pwd:1uk7e724qc9d4622w8v24z375ib1vg43\r\na=fingerprint:sha-256 66:CF:6E:3E:8F:63:77:AE:D0:C0:67:B0:E7:4B:01:45:0D:4E:38:C2:74:09:FF:BE:D0:4C:06:1B:06:17:55:67\r\na=setup:passive\r\na=mid:0\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 minptime=10;useinbandfec=1\r\na=candidate:0 1 udp 2130706431 ************** 8000 typ host generation 0\r\na=candidate:1 1 udp 2130706431 ************ 8000 typ host generation 0\r\nm=video 9 UDP/TLS/RTP/SAVPF 109 123\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:a9a86s2w\r\na=ice-pwd:1uk7e724qc9d4622w8v24z375ib1vg43\r\na=fingerprint:sha-256 66:CF:6E:3E:8F:63:77:AE:D0:C0:67:B0:E7:4B:01:45:0D:4E:38:C2:74:09:FF:BE:D0:4C:06:1B:06:17:55:67\r\na=setup:passive\r\na=mid:1\r\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:109 H264/90000\r\na=rtcp-fb:109 transport-cc\r\na=rtcp-fb:109 nack\r\na=rtcp-fb:109 nack pli\r\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\r\na=rtpmap:123 red/90000\r\na=candidate:0 1 udp 2130706431 ************** 8000 typ host generation 0\r\na=candidate:1 1 udp 2130706431 ************ 8000 typ host generation 0\r\n
[2025-08-14 15:05:23.221][INFO][607300][4qh6jde6] TCP: before dispose resource(HttpConn)(0x6070000345f0), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:05:23.221][WARN][607300][4qh6jde6][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:05:23.221][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:05:23.221][INFO][607300][4qh6jde6] TCP: disposing #0 resource(HttpConn)(0x6070000345f0), conns=1, disposing=1, zombies=0
[2025-08-14 15:05:23.225][INFO][607300][4qh6jde6] RTC: session address init **************:54776
[2025-08-14 15:05:23.225][INFO][607300][4qh6jde6] RTC: session STUN done, waiting DTLS handshake.
[2025-08-14 15:05:23.225][INFO][607300][r928i43c] <- RTC RECV #12, udp 738, pps 18/8, schedule 738
[2025-08-14 15:05:23.228][INFO][607300][4qh6jde6] DTLS: State Passive RECV, done=0, arq=0, r0=153, len=153, cnt=22, size=140, hs=1
[2025-08-14 15:05:23.229][INFO][607300][4qh6jde6] DTLS: State Passive SEND, done=0, arq=0, r0=0, len=706, cnt=22, size=110, hs=2
[2025-08-14 15:05:23.234][INFO][607300][4qh6jde6] DTLS: State Passive RECV, done=0, arq=0, r0=579, len=579, cnt=22, size=299, hs=11
[2025-08-14 15:05:23.235][INFO][607300][4qh6jde6] DTLS: State Passive SEND, done=0, arq=0, r0=0, len=75, cnt=20, size=1, hs=1
[2025-08-14 15:05:23.235][INFO][607300][4qh6jde6] RTC: DTLS handshake done.
[2025-08-14 15:05:23.235][INFO][607300][4qh6jde6] RTC: session pub=1, sub=0, to=30000ms connection established
[2025-08-14 15:05:23.235][INFO][607300][4qh6jde6] RTC: Publisher url=/live/test0814 established
[2025-08-14 15:05:23.237][INFO][607300][4qh6jde6] ignore disabled exec for vhost=__defaultVhost__
[2025-08-14 15:05:23.237][INFO][607300][4qh6jde6] http: mount flv stream for sid=/live/test0814, mount=/live/test0814.flv
[2025-08-14 15:05:23.252][INFO][607300][4qh6jde6] RTC: Discard no-sync Audio, ssrc=973802362, seq=31088, ts=455597083, state=-1
[2025-08-14 15:05:23.773][INFO][607300][4qh6jde6] NACK: update seq=15311, nack range [15310, 15311]
[2025-08-14 15:05:24.571][INFO][607300][4qh6jde6] RTC: Accept sync Video, ssrc=330064030, seq=15353, ts=2259663633, state=0
[2025-08-14 15:05:26.972][INFO][607300][4qh6jde6] 7B audio sh, codec(10, profile=LC, 2channels, 0kbps, 48000HZ), flv(16bits, 2channels, 44100HZ)
[2025-08-14 15:05:26.992][INFO][607300][4qh6jde6] time diff to large=4096910354, next out=4184078639, new pkt=4184078659, set to new pkt
[2025-08-14 15:05:27.144][INFO][607300][68l1dw2j] Hybrid cpu=1.00%,138MB, cid=6,3, timer=62,4,19, clock=0,47,1,0,0,0,0,0,0, free=1, objs=(pkt:57,raw:28,fua:29,msg:58,oth:1,buf:43)
[2025-08-14 15:05:27.144][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(11,rtp:11,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:1), rtcp=(pli:0,twcc:1,rr:1), snk=(39,a:19,v:19,h:0), fid=(id:1,fid:11,ffid:0,addr:1,faddr:11)
[2025-08-14 15:05:27.295][INFO][607300][4qh6jde6] 34B video sh, codec(7, profile=Baseline, level=3, 480x270, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:05:27.295][INFO][607300][4qh6jde6] set ts=2259908433, header=15509, lost=15510
[2025-08-14 15:05:27.756][INFO][607300][4qh6jde6] NACK: update seq=15564, nack range [15563, 15564]
[2025-08-14 15:05:27.798][INFO][607300][57o84c06] Process: cpu=2.00%,140MB, threads=2
[2025-08-14 15:05:27.977][INFO][607300][2282wlx5] HTTP #0 **************:58188 GET http://**************:1985/api/v1/streams/, content-length=-1
[2025-08-14 15:05:27.978][INFO][607300][2282wlx5] TCP: before dispose resource(HttpConn)(0x60700003dce0), conns=1, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:05:27.978][WARN][607300][2282wlx5][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:05:27.978][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=1, removing=0, unsubs=0
[2025-08-14 15:05:27.978][INFO][607300][2282wlx5] TCP: disposing #0 resource(HttpConn)(0x60700003dce0), conns=1, disposing=1, zombies=0
[2025-08-14 15:05:27.981][INFO][607300][c184y43g] HTTP #0 **************:34002 GET http://**************:8080/live/test0814.flv, content-length=-1
[2025-08-14 15:05:27.981][INFO][607300][c184y43g] dispatch cached gop success. count=0, duration=19
[2025-08-14 15:05:27.981][INFO][607300][c184y43g] create consumer, active=1, queue_size=30000ms, jitter=1
[2025-08-14 15:05:27.981][INFO][607300][c184y43g] FLV /live/test0814.flv, encoder=FLV, mw_sleep=350ms, cache=0, msgs=128, dinm=1, guess_av=1/1/1
[2025-08-14 15:05:27.981][INFO][607300][c184y43g] FLV: write header audio=1, video=1, dinm=1, config=1/1/1
[2025-08-14 15:05:27.999][INFO][607300][42opnsp3] HTTP #0 **************:34006 GET http://**************:8080/live/test0814.flv, content-length=-1
[2025-08-14 15:05:28.000][INFO][607300][42opnsp3] dispatch cached gop success. count=0, duration=19
[2025-08-14 15:05:28.000][INFO][607300][42opnsp3] create consumer, active=1, queue_size=30000ms, jitter=1
[2025-08-14 15:05:28.000][INFO][607300][42opnsp3] FLV /live/test0814.flv, encoder=FLV, mw_sleep=350ms, cache=0, msgs=128, dinm=1, guess_av=1/1/1
[2025-08-14 15:05:28.000][INFO][607300][42opnsp3] FLV: write header audio=1, video=1, dinm=1, config=1/1/1
[2025-08-14 15:05:28.332][INFO][607300][c184y43g] TCP: before dispose resource(HttpConn)(0x60700003e290), conns=2, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:05:28.332][WARN][607300][c184y43g][11] client disconnect peer. ret=1007
[0m[2025-08-14 15:05:28.332][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=2, removing=0, unsubs=0
[2025-08-14 15:05:28.332][INFO][607300][c184y43g] TCP: disposing #0 resource(HttpConn)(0x60700003e290), conns=2, disposing=1, zombies=0
[2025-08-14 15:05:29.053][INFO][607300][4qh6jde6] NACK: update seq=15770, nack range [15769, 15770]
[2025-08-14 15:05:29.175][INFO][607300][80c8ibap] RTC: to rtmp bridge request key frame, ssrc=330064030, publisher cid=4qh6jde6
[2025-08-14 15:05:29.175][INFO][607300][80c8ibap] RTC: Need PLI ssrc=330064030, play=[4qh6jde6], publish=[4qh6jde6], count=1/1
[2025-08-14 15:05:29.175][INFO][607300][4qh6jde6] RTC: Request PLI ssrc=330064030, play=[4qh6jde6], count=1/1, bytes=12B
[2025-08-14 15:05:29.214][INFO][607300][4qh6jde6] 34B video sh, codec(7, profile=Baseline, level=3, 480x270, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:05:29.214][INFO][607300][4qh6jde6] set ts=2260081233, header=15791, lost=15792
[2025-08-14 15:05:29.677][INFO][607300][4qh6jde6] NACK: update seq=15880, nack range [15879, 15880]
[2025-08-14 15:05:30.254][INFO][607300][4qh6jde6] NACK: update seq=15983, nack range [15982, 15983]
[2025-08-14 15:05:30.846][INFO][607300][4qh6jde6] NACK: update seq=16098, nack range [16097, 16098]
[2025-08-14 15:05:31.316][INFO][607300][4qh6jde6] 35B video sh, codec(7, profile=Baseline, level=3.1, 640x360, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:05:31.316][INFO][607300][4qh6jde6] set ts=2260269963, header=16183, lost=16184
[2025-08-14 15:05:31.376][INFO][607300][4qh6jde6] NACK: update seq=16204, nack range [16203, 16204]
[2025-08-14 15:05:31.621][INFO][607300][4qh6jde6] NACK: update seq=16236, nack range [16233, 16236]
[2025-08-14 15:05:31.632][INFO][607300][4qh6jde6] NACK: update seq=31509, nack range [31507, 31509]
[2025-08-14 15:05:31.984][INFO][607300][4qh6jde6] NACK: update seq=16291, nack range [16290, 16291]
[2025-08-14 15:05:32.144][INFO][607300][68l1dw2j] Hybrid cpu=5.00%,145MB, cid=6,3, timer=62,4,19, clock=0,47,1,0,0,0,0,0,0, free=1, objs=(pkt:57,raw:28,fua:29,msg:58,oth:1,buf:43)
[2025-08-14 15:05:32.144][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(11,rtp:11,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:1), rtcp=(pli:0,twcc:1,rr:1), snk=(39,a:19,v:19,h:0), fid=(id:1,fid:11,ffid:0,addr:1,faddr:11)
[2025-08-14 15:05:32.449][INFO][607300][4qh6jde6] NACK: update seq=16365, nack range [16364, 16365]
[2025-08-14 15:05:32.803][INFO][607300][57o84c06] Process: cpu=5.00%,147MB, threads=2
[2025-08-14 15:05:33.008][INFO][607300][4qh6jde6] NACK: update seq=16475, nack range [16474, 16475]
[2025-08-14 15:05:33.106][INFO][607300][4qh6jde6] NACK: update seq=16492, nack range [16491, 16492]
[2025-08-14 15:05:33.214][INFO][607300][r928i43c] <- RTC RECV #12, udp 1779, pps 21/177, schedule 1779
[2025-08-14 15:05:34.050][INFO][607300][4qh6jde6] NACK: update seq=16678, nack range [16677, 16678]
[2025-08-14 15:05:34.276][INFO][607300][4qh6jde6] NACK: update seq=16723, nack range [16722, 16723]
[2025-08-14 15:05:34.450][INFO][607300][4qh6jde6] NACK: update seq=16753, nack range [16752, 16753]
[2025-08-14 15:05:34.642][INFO][607300][4qh6jde6] NACK: update seq=16795, nack range [16794, 16795]
[2025-08-14 15:05:35.209][INFO][607300][80c8ibap] RTC: to rtmp bridge request key frame, ssrc=330064030, publisher cid=4qh6jde6
[2025-08-14 15:05:35.267][INFO][607300][4qh6jde6] 35B video sh, codec(7, profile=Baseline, level=3.1, 640x360, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:05:35.267][INFO][607300][4qh6jde6] set ts=2260625553, header=16907, lost=16908
[2025-08-14 15:05:35.364][INFO][607300][4qh6jde6] 35B video sh, codec(7, profile=Baseline, level=3.2, 960x540, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:05:35.364][INFO][607300][4qh6jde6] set ts=2260632843, header=16937, lost=16938
[2025-08-14 15:05:37.144][INFO][607300][68l1dw2j] Hybrid cpu=5.00%,152MB, cid=6,3, timer=62,4,19, clock=0,47,1,0,0,0,0,0,0, free=1, objs=(pkt:57,raw:28,fua:29,msg:58,oth:1,buf:43)
[2025-08-14 15:05:37.144][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(11,rtp:11,stun:1,rtcp:1), spkts=(1,rtp:0,stun:1,rtcp:1), rtcp=(pli:0,twcc:1,rr:1), snk=(39,a:19,v:19,h:0), fid=(id:1,fid:11,ffid:0,addr:1,faddr:11)
[2025-08-14 15:05:37.808][INFO][607300][57o84c06] Process: cpu=5.00%,154MB, threads=2
[2025-08-14 15:05:38.167][INFO][607300][42opnsp3] -> HTS http: got 27 msgs, age=10154127, min=8, mw=350
[2025-08-14 15:05:38.837][INFO][607300][4qh6jde6] NACK: update seq=17694, nack range [17693, 17694]
[2025-08-14 15:05:38.900][INFO][607300][4qh6jde6] NACK: update seq=17712, nack range [17711, 17712]
[2025-08-14 15:05:41.239][INFO][607300][80c8ibap] RTC: to rtmp bridge request key frame, ssrc=330064030, publisher cid=4qh6jde6
[2025-08-14 15:05:41.240][INFO][607300][80c8ibap] RTC: Need PLI ssrc=330064030, play=[4qh6jde6], publish=[4qh6jde6], count=3/3
[2025-08-14 15:05:41.240][INFO][607300][4qh6jde6] RTC: Request PLI ssrc=330064030, play=[4qh6jde6], count=3/3, bytes=12B
[2025-08-14 15:05:41.258][INFO][607300][4qh6jde6] 35B video sh, codec(7, profile=Baseline, level=3.2, 960x540, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:05:41.258][INFO][607300][4qh6jde6] set ts=2261164203, header=18258, lost=18259
[2025-08-14 15:05:41.345][INFO][607300][4qh6jde6] NACK: update seq=18302, nack range [18301, 18302]
[2025-08-14 15:05:42.022][INFO][607300][4qh6jde6] NACK: update seq=18424, nack range [18423, 18424]
[2025-08-14 15:05:42.145][INFO][607300][68l1dw2j] Hybrid cpu=5.00%,160MB, cid=6,6, timer=62,11,48, clock=0,44,3,0,0,0,0,0,0, free=1, objs=(pkt:443,raw:50,fua:392,msg:595,oth:1,buf:246)
[2025-08-14 15:05:42.145][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(251,rtp:246,stun:1,rtcp:4), spkts=(14,rtp:0,stun:1,rtcp:28), rtcp=(pli:1,twcc:10,rr:1), snk=(97,a:48,v:48,h:1), fid=(id:0,fid:251,ffid:0,addr:1,faddr:251)
[2025-08-14 15:05:42.693][INFO][607300][4qh6jde6] NACK: update seq=18580, nack range [18579, 18580]
[2025-08-14 15:05:42.814][INFO][607300][57o84c06] Process: cpu=5.99%,162MB, threads=2
[2025-08-14 15:05:43.157][INFO][607300][4qh6jde6] NACK: update seq=18688, nack range [18687, 18688]
[2025-08-14 15:05:43.190][INFO][607300][4qh6jde6] NACK: update seq=18696, nack range [18695, 18696]
[2025-08-14 15:05:43.241][INFO][607300][r928i43c] <- RTC RECV #12, udp 2757, pps 24/274, schedule 2757
[2025-08-14 15:05:44.276][INFO][607300][4qh6jde6] NACK: update seq=18956, nack range [18955, 18956]
[2025-08-14 15:05:44.549][INFO][607300][4qh6jde6] NACK: update seq=19015, nack range [19014, 19015]
[2025-08-14 15:05:45.108][INFO][607300][4qh6jde6] NACK: update seq=19137, nack range [19136, 19137]
[2025-08-14 15:05:45.908][INFO][607300][4qh6jde6] NACK: update seq=19324, nack range [19323, 19324]
[2025-08-14 15:05:46.900][INFO][607300][4qh6jde6] NACK: update seq=19557, nack range [19556, 19557]
[2025-08-14 15:05:47.146][INFO][607300][68l1dw2j] Hybrid cpu=6.00%,168MB, cid=6,6, timer=62,11,48, clock=0,44,3,0,0,0,0,0,0, free=1, objs=(pkt:443,raw:50,fua:392,msg:595,oth:1,buf:246)
[2025-08-14 15:05:47.146][INFO][607300][68l1dw2j] RTC: Server conns=1, rpkts=(251,rtp:246,stun:1,rtcp:4), spkts=(14,rtp:0,stun:1,rtcp:28), rtcp=(pli:1,twcc:10,rr:1), snk=(97,a:48,v:48,h:1), fid=(id:0,fid:251,ffid:0,addr:1,faddr:251)
[2025-08-14 15:05:47.269][INFO][607300][80c8ibap] RTC: to rtmp bridge request key frame, ssrc=330064030, publisher cid=4qh6jde6
[2025-08-14 15:05:47.303][INFO][607300][4qh6jde6] 35B video sh, codec(7, profile=Baseline, level=3.2, 960x540, 0kbps, 0.0fps, 0.0s)
[2025-08-14 15:05:47.303][INFO][607300][4qh6jde6] set ts=2261708433, header=19646, lost=19647
[2025-08-14 15:05:47.392][INFO][607300][4qh6jde6] NACK: update seq=19689, nack range [19688, 19689]
[2025-08-14 15:05:47.442][INFO][607300][4qh6jde6] NACK: update seq=19699, nack range [19697, 19699]
[2025-08-14 15:05:47.819][INFO][607300][57o84c06] Process: cpu=5.00%,170MB, threads=2
[2025-08-14 15:05:48.133][INFO][607300][4qh6jde6] NACK: update seq=19832, nack range [19831, 19832]
[2025-08-14 15:05:48.335][INFO][607300][42opnsp3] -> HTS http: got 27 msgs, age=20331045, min=8, mw=350
[2025-08-14 15:05:48.565][INFO][607300][4qh6jde6] NACK: update seq=19933, nack range [19932, 19933]
[2025-08-14 15:05:48.757][INFO][607300][y8e3956s] HTTP #0 **************:51219 GET http://**************:1985/api/v1/summaries, content-length=-1
[2025-08-14 15:05:48.773][INFO][607300][4qh6jde6] NACK: update seq=19983, nack range [19982, 19983]
[2025-08-14 15:05:48.804][INFO][607300][4qh6jde6] NACK: update seq=19988, nack range [19987, 19988]
[2025-08-14 15:05:48.808][INFO][607300][y8e3956s] TCP: before dispose resource(HttpConn)(0x60700003e990), conns=2, zombies=0, ign=0, inz=0, ind=0
[33m[2025-08-14 15:05:48.808][WARN][607300][y8e3956s][104] client disconnect peer. ret=1007
[0m[2025-08-14 15:05:48.808][INFO][607300][u4rb4168] TCP: clear zombies=1 resources, conns=2, removing=0, unsubs=0
[2025-08-14 15:05:48.808][INFO][607300][y8e3956s] TCP: disposing #0 resource(HttpConn)(0x60700003e990), conns=2, disposing=1, zombies=0
[2025-08-14 15:05:48.869][INFO][607300][4qh6jde6] NACK: update seq=20006, nack range [20005, 20006]
[2025-08-14 15:05:49.032][INFO][607300][4qh6jde6] NACK: update seq=20047, nack range [20046, 20047]
[2025-08-14 15:05:49.492][INFO][607300][4qh6jde6] NACK: update seq=20156, nack range [20155, 20156]
[2025-08-14 15:05:50.095][INFO][607300][4qh6jde6] DTLS: After done, got 39 bytes
[2025-08-14 15:05:50.095][INFO][607300][4qh6jde6] DTLS: State Passive RECV, done=1, arq=0, r0=39, len=39, cnt=21, size=26, hs=0
[33m[2025-08-14 15:05:50.095][WARN][607300][4qh6jde6][0] DTLS: SSL3 alert method=read type=warning, desc=CN(close notify), where=16388, ret=256, r1=0
[0m[2025-08-14 15:05:50.096][INFO][607300][4qh6jde6] RTC: session destroy by DTLS alert(warning CN), username=a9a86s2w:wQe+
[2025-08-14 15:05:50.096][INFO][607300][4qh6jde6] RTC: before dispose resource(RtcConn)(0x61d000024e80), conns=1, zombies=0, ign=0, inz=0, ind=0
[2025-08-14 15:05:50.096][INFO][607300][4qh6jde6] RTC: session detach from [4qh6jde6](RtcConn), disposing=1
[2025-08-14 15:05:50.096][INFO][607300][i036lpvl] RTC: clear zombies=1 resources, conns=1, removing=0, unsubs=1
[2025-08-14 15:05:50.096][INFO][607300][4qh6jde6] RTC: disposing #0 resource(RtcConn)(0x61d000024e80), conns=1, disposing=1, zombies=0
[2025-08-14 15:05:50.096][INFO][607300][4qh6jde6] cleanup when unpublish, created=1, deliver=1
[2025-08-14 15:05:50.096][INFO][607300][4qh6jde6] Qavg: 197.523
[33m[2025-08-14 15:05:50.096][WARN][607300][4qh6jde6][11] 2 frames left in the queue on closing
[0m[2025-08-14 15:05:50.096][INFO][607300][4qh6jde6] cleanup when unpublish
[2025-08-14 15:05:50.096][INFO][607300][4qh6jde6] http: unmount flv stream for sid=/live/test0814, i=0
[33m[2025-08-14 15:05:50.098][WARN][607300][4qh6jde6][4][DTLS_HANG] DTLS: Hang, done=0, version=-1, arq=0
[0m=================================================================
==607300==ERROR: AddressSanitizer: heap-use-after-free on address 0x604000611418 at pc 0x562f59ef0a87 bp 0x7fd4645a3c30 sp 0x7fd4645a3c20
READ of size 8 at 0x604000611418 thread T1 (srs-hybrid-2)
    #0 0x562f59ef0a86 in SrsLiveStream::do_serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/app/srs_app_http_stream.cpp:748
    #1 0x562f59eee23e in SrsLiveStream::serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/app/srs_app_http_stream.cpp:632
    #2 0x562f59dbea54 in SrsHttpServeMux::serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/protocol/srs_protocol_http_stack.cpp:794
    #3 0x562f5a0404c3 in SrsHttpServer::serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/app/srs_app_http_conn.cpp:543
    #4 0x562f59dc20d9 in SrsHttpAuthMux::serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/protocol/srs_protocol_http_stack.cpp:985
    #5 0x562f59dc14f9 in SrsHttpCorsMux::serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/protocol/srs_protocol_http_stack.cpp:952
    #6 0x562f5a03c13a in SrsHttpConn::process_request(ISrsHttpResponseWriter*, ISrsHttpMessage*, int) src/app/srs_app_http_conn.cpp:235
    #7 0x562f5a03b854 in SrsHttpConn::process_requests(SrsRequest**) src/app/srs_app_http_conn.cpp:208
    #8 0x562f5a03acd1 in SrsHttpConn::do_cycle() src/app/srs_app_http_conn.cpp:162
    #9 0x562f5a03a156 in SrsHttpConn::cycle() src/app/srs_app_http_conn.cpp:107
    #10 0x562f59f02185 in SrsFastCoroutine::cycle() src/app/srs_app_st.cpp:309
    #11 0x562f59f022d5 in SrsFastCoroutine::pfn(void*) src/app/srs_app_st.cpp:324
    #12 0x562f5a2cc68f in _st_thread_main /data/MedicalRecord/srs/trunk/objs/Platform-SRS6-Linux-5.4.0-GCC9.4.0-x86_64/st-srs/sched.c:380
    #13 0x562f5a2ccfb5 in st_thread_create /data/MedicalRecord/srs/trunk/objs/Platform-SRS6-Linux-5.4.0-GCC9.4.0-x86_64/st-srs/sched.c:666
    #14 0x60400064704f  (<unknown module>)

0x604000611418 is located 8 bytes inside of 48-byte region [0x604000611410,0x604000611440)
freed by thread T1 (srs-hybrid-2) here:
    #0 0x562f59bb9c1f in operator delete(void*) (/data/MedicalRecord/srs/trunk/objs/srs+0x4dcc1f)

previously allocated by thread T1 (srs-hybrid-2) here:
    #0 0x562f59bb8c87 in operator new(unsigned long) (/data/MedicalRecord/srs/trunk/objs/srs+0x4dbc87)

Thread T1 (srs-hybrid-2) created by T0 here:
    #0 0x562f59ae3f45 in pthread_create (/data/MedicalRecord/srs/trunk/objs/srs+0x406f45)
    #1 0x562f5a14c26f in SrsThreadPool::execute(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, SrsCplxError* (*)(void*), void*) src/app/srs_app_threads.cpp:825
    #2 0x562f5a2cafbf in run_in_thread_pool() src/main/srs_main_server.cpp:478
    #3 0x562f5a2caa27 in run_directly_or_daemon() src/main/srs_main_server.cpp:417
    #4 0x562f5a2c82be in do_main(int, char**, char**) src/main/srs_main_server.cpp:245
    #5 0x562f5a2c85d9 in main src/main/srs_main_server.cpp:256
    #6 0x7fd46a06d082 in __libc_start_main (/lib/x86_64-linux-gnu/libc.so.6+0x24082)

SUMMARY: AddressSanitizer: heap-use-after-free src/app/srs_app_http_stream.cpp:748 in SrsLiveStream::do_serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*)
Shadow bytes around the buggy address:
  0x0c08800ba230: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
  0x0c08800ba240: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
  0x0c08800ba250: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
  0x0c08800ba260: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
  0x0c08800ba270: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
=>0x0c08800ba280: fa fa fd[fd]fd fd fd fd fa fa fd fd fd fd fd fa
  0x0c08800ba290: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
  0x0c08800ba2a0: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
  0x0c08800ba2b0: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fd
  0x0c08800ba2c0: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
  0x0c08800ba2d0: fa fa fd fd fd fd fd fd fa fa fd fd fd fd fd fd
Shadow byte legend (one shadow byte represents 8 application bytes):
  Addressable:           00
  Partially addressable: 01 02 03 04 05 06 07 
  Heap left redzone:       fa
  Freed heap region:       fd
  Stack left redzone:      f1
  Stack mid redzone:       f2
  Stack right redzone:     f3
  Stack after return:      f5
  Stack use after scope:   f8
  Global redzone:          f9
  Global init order:       f6
  Poisoned by user:        f7
  Container overflow:      fc
  Array cookie:            ac
  Intra object redzone:    bb
  ASan internal:           fe
  Left alloca redzone:     ca
  Right alloca redzone:    cb
  Shadow gap:              cc
[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0] =================================================================
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0] ==607300==ERROR: AddressSanitizer: heap-use-after-free on address 0x604000611418 at pc 0x562f59ef0a87 bp 0x7fd4645a3c30 sp 0x7fd4645a3c20
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0] READ of size 8 at 0x604000611418 thread T1 (srs-hybrid-2)
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #0 0x562f59ef0a86 in SrsLiveStream::do_serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/app/srs_app_http_stream.cpp:748, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #1 0x562f59eee23e in SrsLiveStream::serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/app/srs_app_http_stream.cpp:632, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #2 0x562f59dbea54 in SrsHttpServeMux::serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/protocol/srs_protocol_http_stack.cpp:794, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #3 0x562f5a0404c3 in SrsHttpServer::serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/app/srs_app_http_conn.cpp:543, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #4 0x562f59dc20d9 in SrsHttpAuthMux::serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/protocol/srs_protocol_http_stack.cpp:985, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #5 0x562f59dc14f9 in SrsHttpCorsMux::serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*) src/protocol/srs_protocol_http_stack.cpp:952, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #6 0x562f5a03c13a in SrsHttpConn::process_request(ISrsHttpResponseWriter*, ISrsHttpMessage*, int) src/app/srs_app_http_conn.cpp:235, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #7 0x562f5a03b854 in SrsHttpConn::process_requests(SrsRequest**) src/app/srs_app_http_conn.cpp:208, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #8 0x562f5a03acd1 in SrsHttpConn::do_cycle() src/app/srs_app_http_conn.cpp:162, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #9 0x562f5a03a156 in SrsHttpConn::cycle() src/app/srs_app_http_conn.cpp:107, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #10 0x562f59f02185 in SrsFastCoroutine::cycle() src/app/srs_app_st.cpp:309, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #11 0x562f59f022d5 in SrsFastCoroutine::pfn(void*) src/app/srs_app_st.cpp:324, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #12 0x562f5a2cc68f in _st_thread_main /data/MedicalRecord/srs/trunk/objs/Platform-SRS6-Linux-5.4.0-GCC9.4.0-x86_64/st-srs/sched.c:380, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #13 0x562f5a2ccfb5 in st_thread_create /data/MedicalRecord/srs/trunk/objs/Platform-SRS6-Linux-5.4.0-GCC9.4.0-x86_64/st-srs/sched.c:666, r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0]     #14 0x60400064704f  (<unknown module>), r0=1093
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0] 0x604000611418 is located 8 bytes inside of 48-byte region [0x604000611410,0x604000611440)
[0m[31m[2025-08-14 15:05:50.486][ERROR][607300][42opnsp3][0] freed by thread T1 (srs-hybrid-2) here:
[0m[31m[2025-08-14 15:05:50.523][ERROR][607300][42opnsp3][0]     #0 0x562f59bb9c1f in operator delete(void*) (/data/MedicalRecord/srs/trunk/objs/srs+0x4dcc1f), 0x00000
[0m[31m[2025-08-14 15:05:50.523][ERROR][607300][42opnsp3][0] previously allocated by thread T1 (srs-hybrid-2) here:
[0m[31m[2025-08-14 15:05:50.560][ERROR][607300][42opnsp3][0]     #0 0x562f59bb8c87 in operator new(unsigned long) (/data/MedicalRecord/srs/trunk/objs/srs+0x4dbc87), 0x00000
[0m[31m[2025-08-14 15:05:50.560][ERROR][607300][42opnsp3][0] Thread T1 (srs-hybrid-2) created by T0 here:
[0m[31m[2025-08-14 15:05:50.591][ERROR][607300][42opnsp3][0]     #0 0x562f59ae3f45 in pthread_create (/data/MedicalRecord/srs/trunk/objs/srs+0x406f45), 0x00000
[0m[31m[2025-08-14 15:05:50.592][ERROR][607300][42opnsp3][0]     #1 0x562f5a14c26f in SrsThreadPool::execute(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, SrsCplxError* (*)(void*), void*) src/app/srs_app_threads.cpp:825, r0=1093
[0m[31m[2025-08-14 15:05:50.592][ERROR][607300][42opnsp3][0]     #2 0x562f5a2cafbf in run_in_thread_pool() src/main/srs_main_server.cpp:478, r0=1093
[0m[31m[2025-08-14 15:05:50.592][ERROR][607300][42opnsp3][0]     #3 0x562f5a2caa27 in run_directly_or_daemon() src/main/srs_main_server.cpp:417, r0=1093
[0m[31m[2025-08-14 15:05:50.592][ERROR][607300][42opnsp3][0]     #4 0x562f5a2c82be in do_main(int, char**, char**) src/main/srs_main_server.cpp:245, r0=1093
[0m[31m[2025-08-14 15:05:50.592][ERROR][607300][42opnsp3][0]     #5 0x562f5a2c85d9 in main src/main/srs_main_server.cpp:256, r0=1093
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]     #6 0x7fd46a06d082 in __libc_start_main (/lib/x86_64-linux-gnu/libc.so.6+0x24082), 0x00000
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0] SUMMARY: AddressSanitizer: heap-use-after-free src/app/srs_app_http_stream.cpp:748 in SrsLiveStream::do_serve_http(ISrsHttpResponseWriter*, ISrsHttpMessage*)
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0] Shadow bytes around the buggy address:
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   0x0c08800ba230: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   0x0c08800ba240: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   0x0c08800ba250: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   0x0c08800ba260: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   0x0c08800ba270: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0] =>0x0c08800ba280: fa fa fd[fd]fd fd fd fd fa fa fd fd fd fd fd fa
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   0x0c08800ba290: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   0x0c08800ba2a0: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   0x0c08800ba2b0: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fd
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   0x0c08800ba2c0: fa fa fd fd fd fd fd fa fa fa fd fd fd fd fd fa
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   0x0c08800ba2d0: fa fa fd fd fd fd fd fd fa fa fd fd fd fd fd fd
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0] Shadow byte legend (one shadow byte represents 8 application bytes):
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Addressable:           00
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Partially addressable: 01 02 03 04 05 06 07 
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Heap left redzone:       fa
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Freed heap region:       fd
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Stack left redzone:      f1
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Stack mid redzone:       f2
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Stack right redzone:     f3
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Stack after return:      f5
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Stack use after scope:   f8
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Global redzone:          f9
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Global init order:       f6
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Poisoned by user:        f7
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Container overflow:      fc
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Array cookie:            ac
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Intra object redzone:    bb
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   ASan internal:           fe
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Left alloca redzone:     ca
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Right alloca redzone:    cb
[0m[31m[2025-08-14 15:05:50.625][ERROR][607300][42opnsp3][0]   Shadow gap:              cc
[0m==607300==ABORTING
