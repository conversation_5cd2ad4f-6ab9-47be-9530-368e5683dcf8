2025-08-14 14:54:36,908 - __main__ - INFO - 确保目录存在: /data/MedicalRecord/recording_ori
2025-08-14 14:54:36,908 - __main__ - INFO - 确保目录存在: /data/MedicalRecord/recordings/temp
2025-08-14 14:54:36,908 - __main__ - INFO - 确保目录存在: /data/MedicalRecord/logs/recording
2025-08-14 14:54:36,910 - config_loader - INFO - 配置验证通过
2025-08-14 14:54:36,911 - __main__ - INFO - 启动录制服务...
2025-08-14 14:54:36,911 - __main__ - INFO - 配置文件: /data/MedicalRecord/config/config.yaml
2025-08-14 14:54:36,911 - __main__ - INFO - 服务器配置: {'server_host': '0.0.0.0', 'server_port': 5410, 'srs_host': '**************', 'srs_api_port': 1985, 'srs_http_port': 8080, 'srs_webrtc_port': 8000, 'max_concurrent_recordings': 10, 'recording_timeout': 3600}
 * Serving Flask app 'recording_server'
 * Debug mode: off
2025-08-14 14:54:36,912 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5410
 * Running on http://**************:5410
2025-08-14 14:54:36,912 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-14 14:54:39,762 - werkzeug - INFO - 127.0.0.1 - - [14/Aug/2025 14:54:39] "GET /api/health HTTP/1.1" 200 -
2025-08-14 14:56:53,517 - werkzeug - INFO - ************** - - [14/Aug/2025 14:56:53] "GET /api/health HTTP/1.1" 200 -
2025-08-14 15:02:13,813 - werkzeug - INFO - ************** - - [14/Aug/2025 15:02:13] "[33mOPTIONS /api/signal/start HTTP/1.1[0m" 404 -
2025-08-14 15:03:22,762 - werkzeug - INFO - ************** - - [14/Aug/2025 15:03:22] "OPTIONS /api/recording/video/start HTTP/1.1" 200 -
2025-08-14 15:03:22,776 - __main__ - INFO - 流 test0814 可用性检查: False (SRS API)
2025-08-14 15:03:22,777 - werkzeug - INFO - ************** - - [14/Aug/2025 15:03:22] "[31m[1mPOST /api/recording/video/start HTTP/1.1[0m" 400 -
2025-08-14 15:05:27,964 - werkzeug - INFO - ************** - - [14/Aug/2025 15:05:27] "OPTIONS /api/recording/video/start HTTP/1.1" 200 -
2025-08-14 15:05:27,978 - __main__ - INFO - 流 test0814 可用性检查: True (SRS API)
2025-08-14 15:05:27,982 - __main__ - INFO - 使用HTTP-FLV输入源: http://**************:8080/live/test0814.flv
2025-08-14 15:05:27,982 - __main__ - INFO - FFmpeg video 录制命令: ffmpeg -i http://**************:8080/live/test0814.flv -c copy -f mp4 -y -movflags +faststart /data/MedicalRecord/recordings/temp/test0814_20250814_150527_temp.mp4
2025-08-14 15:05:27,987 - __main__ - INFO - 开始录制任务 VIDEO_20250814070526, 流 test0814, PID: 620296
2025-08-14 15:05:27,988 - werkzeug - INFO - ************** - - [14/Aug/2025 15:05:27] "POST /api/recording/video/start HTTP/1.1" 200 -
2025-08-14 15:05:50,650 - __main__ - INFO - 录制进程 VIDEO_20250814070526 正常结束
2025-08-14 15:05:50,650 - __main__ - INFO - 录制文件已保存: /data/MedicalRecord/recording_ori/video/test0814/20250814_150527.mp4
