# 电子病历生成系统 - WebRTC实时推流与录制

## 📋 项目概述

本项目是一个基于WebRTC技术的电子病历生成系统，支持实时音视频推流、录制和语音识别。系统采用SRS 6.0作为流媒体服务器，结合FunASR进行语音识别和说话人分离，为医疗场景提供完整的音视频记录解决方案。

## 🎯 主要功能

- **WebRTC实时推流**: 浏览器端实时音视频推流
- **多路流录制**: 支持多路并发推流和录制
- **语音识别**: 基于FunASR的实时语音转文字
- **说话人分离**: 自动识别和分离不同说话人
- **热词支持**: 医疗专业术语识别优化
- **跨平台支持**: Windows、macOS、Linux全平台支持

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端WebRTC    │───▶│   SRS服务器     │───▶│   录制服务      │
│   推流客户端    │    │  (流媒体转发)   │    │  (FFmpeg录制)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   FunASR服务    │
                       │  (语音识别)     │
                       └─────────────────┘
```

## 🚀 快速开始

### 1. 环境要求

- **操作系统**: Ubuntu 18.04+ / CentOS 7+ / Windows 10+
- **Python**: 3.8+
- **Node.js**: 14+ (可选，用于HTTP服务器)
- **内存**: 8GB以上
- **网络**: 支持UDP传输

### 2. 安装依赖

#### SRS服务器依赖
```bash
# Ubuntu/Debian
sudo apt install -y build-essential git cmake pkg-config
sudo apt install -y libssl-dev libopus-dev libsrtp2-dev

# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y openssl-devel opus-devel libsrtp-devel
```

#### Python依赖
```bash
pip install -U funasr modelscope ffmpeg-python pydub torch
```

### 3. 启动服务

#### 启动SRS流媒体服务器
```bash
# 进入SRS目录
cd srs/trunk

# 设置候选IP地址
export CANDIDATE=your-server-ip

# 启动SRS服务器
./objs/srs -c conf/rtc.conf
```

#### 使用外部WebRTC客户端
```bash
# 使用外部客户端连接WebRTC推流地址
# webrtc://your-server-ip:1985/rtc/v1/whip/?app=live&stream=test

# 访问测试页面
# http://localhost:8080/web/webrtc-test.html
```

#### 启动语音识别服务
```bash
# 下载模型
python funasr/download_model.py

# 启动FunASR服务
python funasr/app.py
```

## 🔧 配置说明

### SRS服务器配置
- **HTTP API端口**: 1985
- **WebRTC UDP端口**: 8000
- **HTTP服务端口**: 8080
- **配置文件**: `srs/trunk/conf/rtc.conf`

### 前端配置
- **服务器地址**: `http://server-ip:1985`
- **推流地址格式**: `webrtc://server-ip:8000/live/streamkey`
- **支持的浏览器**: Chrome 60+, Firefox 60+, Safari 12+

### 语音识别配置
- **热词文件**: `funasr/hotwords.txt`
- **模型路径**: `~/.cache/modelscope/hub/models/iic/`
- **支持格式**: WAV, MP3, MP4, FLV

## 📁 项目结构

```
MedicalRecord/
├── documents/                 # 项目文档
│   ├── SRS部署指南.md
│   ├── 前端WebRTC开发指南.md
│   ├── 01-系统架构设计文档.md
│   └── 02-API接口详细规范.md
├── scripts/                   # 管理脚本
│   ├── start-srs.sh          # SRS启动脚本
│   ├── test-webrtc.sh        # WebRTC功能测试
│   ├── webrtc-security-fix.sh # 安全问题解决方案
│   └── chrome-webrtc-dev.bat # Windows Chrome开发模式
├── web/                       # 前端文件
│   └── webrtc-test.html      # WebRTC测试页面
├── srs/                       # SRS流媒体服务器
│   └── trunk/                # SRS主程序
├── funasr/                    # 语音识别服务
│   ├── app.py                # FunASR主程序
│   ├── download_model.py     # 模型下载脚本
│   └── hotwords.txt          # 热词配置
├── recording_service/         # 录制服务
├── recordings/                # 录制文件存储
└── logs/                      # 日志文件
```

## 🛠️ 开发指南

### 前端开发
详细的前端开发指南请参考：[前端WebRTC开发指南](documents/前端WebRTC开发指南.md)

主要包含：
- WebRTC API使用方法
- SRS服务器通信协议
- 错误处理和调试技巧
- 浏览器兼容性处理

### 服务器部署
详细的服务器部署指南请参考：[SRS部署指南](documents/SRS部署指南.md)

主要包含：
- 服务器环境配置
- SRS编译和安装
- 网络和防火墙设置
- 性能监控和故障排除

## 🧪 测试验证

### 功能测试
```bash
# 测试SRS服务器状态
curl http://server-ip:1985/api/v1/summaries

# 测试WebRTC功能
./scripts/test-webrtc.sh

# 测试端口监听
ss -tuln | grep -E ":(1985|8000|8080)"
```

### 浏览器测试
1. 访问测试页面：`http://localhost:8080/web/webrtc-test.html`
2. 配置服务器地址：`http://server-ip:1985`
3. 设置推流密钥：`test`
4. 点击"开始推流"进行测试

## 🔍 故障排除

### 常见问题

#### WebRTC连接失败
- 检查防火墙端口开放状态
- 确认CANDIDATE IP地址配置正确
- 验证浏览器安全上下文（HTTPS/localhost）

#### 推流"Failed to fetch"错误
- 确认SRS服务器正常运行
- 检查API端点配置（使用1985端口）
- 验证WebRTC API请求格式

#### 语音识别异常
- 检查模型文件下载完整性
- 确认音频格式支持
- 验证热词文件格式

### 日志查看
```bash
# SRS服务器日志
tail -f logs/srs/srs-console.log

# 录制服务日志
tail -f logs/recording/recording.log

# 浏览器控制台
# 打开开发者工具查看WebRTC详细日志
```

## 📊 性能监控

### 系统监控
```bash
# 查看SRS进程状态
top -p $(pgrep srs)

# 查看网络连接
ss -tuln | grep srs

# 查看磁盘使用
df -h
du -sh recordings/
```

### API监控
```bash
# 查看服务器状态
curl -s http://localhost:1985/api/v1/summaries | jq

# 查看活跃流
curl -s http://localhost:1985/api/v1/streams/ | jq
```

## 🔒 安全配置

### 生产环境建议
- 使用HTTPS协议
- 配置防火墙规则
- 设置API访问控制
- 定期更新系统和依赖
- 配置日志轮转和监控

## 📝 更新日志

- **2025-01-08**: WebRTC推流功能完成，修复"Failed to fetch"错误
- **2025-01-08**: 完善项目文档，优化代码结构
- **2024-xx-xx**: 集成FunASR语音识别功能
- **2024-xx-xx**: 实现多路流录制功能

## 📞 技术支持

### 文档资源
- [系统架构设计文档](documents/01-系统架构设计文档.md)
- [API接口详细规范](documents/02-API接口详细规范.md)
- [SRS部署指南](documents/SRS部署指南.md)
- [前端WebRTC开发指南](documents/前端WebRTC开发指南.md)

### 问题反馈
如遇到问题，请提供以下信息：
- 操作系统版本
- 浏览器版本
- 错误信息截图
- 相关日志文件

---

**项目版本**: v2.0
**最后更新**: 2025-01-08
**技术栈**: WebRTC, SRS 6.0, FunASR, FFmpeg
**维护团队**: 医疗信息化开发团队

