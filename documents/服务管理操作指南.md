# 医疗录制系统 - 服务管理操作指南

## 📋 概述

本文档提供医疗录制系统所有服务组件的详细管理操作指南，包括服务启动、关闭、重启、状态检查和故障排查等操作。

## 🏗️ 系统服务架构

### 服务组件列表

| 服务名称 | 功能描述 | 端口 | 状态 | 依赖关系 |
|---------|----------|------|------|----------|
| SRS流媒体服务器 | WebRTC推流、RTMP推流、HTTP-FLV拉流 | 1985,8080,8000 | 必需 | 基础服务 |
| 录制服务 | 视频/音频录制、任务状态管理、FFmpeg控制、自动语音转文字 | 5410 | 必需 | 依赖SRS |
| FunASR语音识别服务 | 语音转文字处理API | 5412 | 可选 | 由录制服务调用 |

### 服务依赖关系图

```
外部WebRTC客户端 ──→ SRS流媒体服务器 ──→ 录制服务 ──→ FFmpeg ──→ 录制文件
                                          │
                                          └──→ FunASR服务 ──→ 语音转文字
                                               (录制完成后自动调用)
```

## 🚀 服务启动操作

### 启动顺序（重要：必须按顺序启动）

1. **SRS流媒体服务器** (基础服务)
2. **FunASR语音识别服务** (可选，用于自动语音转文字)
3. **录制服务** (核心服务，集成语音转文字功能)

### 1. SRS流媒体服务器启动

#### 启动命令
```bash
cd /data/MedicalRecord
./scripts/start-srs.sh start
```
# 检查SRS状态
./scripts/start-srs.sh status

# 启动SRS服务器
./scripts/start-srs.sh start

# 停止SRS服务器
./scripts/start-srs.sh stop

# 重启SRS服务器
./scripts/start-srs.sh restart


#### 启动验证
```bash
# 检查服务状态
./scripts/start-srs.sh status

# 检查API响应
curl http://**************:1985/api/v1/summaries

# 检查端口监听
ss -tuln | grep -E ":(1985|8080|8000)"

# 检查进程状态
ps aux | grep srs | grep -v grep

# 查看实时日志
tail -f /data/MedicalRecord/logs/srs/srs-console.log
```

#### 预期结果
- ✅ 端口1985 (HTTP API) 监听
- ✅ 端口8080 (HTTP-FLV) 监听  
- ✅ 端口8000 (WebRTC) 监听
- ✅ API响应正常
- ✅ 进程存在且运行正常

### 2. 录制服务启动

#### 环境准备
```bash
# 激活conda环境（必需）
conda activate funasr

# 验证环境
echo $CONDA_DEFAULT_ENV
```

#### 启动命令
```bash
cd /data/MedicalRecord/recording_service
./start_recording_service.sh start
```

#### 启动验证
```bash
# 检查服务状态
./start_recording_service.sh status

# 检查健康检查API
curl http://**************:5410/api/health

# 检查端口监听
ss -tuln | grep 5410
```

#### 预期结果
- ✅ 端口5410监听
- ✅ 健康检查API返回成功
- ✅ 日志文件正常生成
- ✅ 进程运行正常

### 3. FunASR语音识别服务启动（可选）

**服务说明**: FunASR语音识别服务提供语音转文字API，由录制服务在录制完成后自动调用。

**启动条件**:
- 需要自动语音转文字功能时
- 录制完成后需要生成文字记录时

#### 启动命令
```bash
cd /data/MedicalRecord/funasr
# 需要先改造为API服务
python funasr_api_server.py
```

#### 启动验证
```bash
# 检查健康检查API
curl http://**************:5412/api/health

# 检查进程
ps aux | grep funasr_api_server.py | grep -v grep

# 检查端口监听
ss -tuln | grep 5412
```

## 🛑 服务关闭操作

### 关闭顺序（重要：按逆序关闭）

1. **录制服务**
2. **FunASR语音识别服务** (如果已启动)
3. **SRS流媒体服务器**

### 详细关闭命令

#### 1. 停止录制服务
```bash
cd /data/MedicalRecord/recording_service
./start_recording_service.sh stop

# 验证关闭成功
./start_recording_service.sh status
```

#### 2. 停止FunASR语音识别服务
```bash
pkill -f "funasr_api_server.py"
```

#### 3. 停止SRS流媒体服务器
```bash
cd /data/MedicalRecord
./scripts/start-srs.sh stop
```

### 关闭验证
```bash
# 检查所有服务端口
ss -tuln | grep -E ":(1985|8080|8000|5410|5412)"

# 检查相关进程
ps aux | grep -E "(srs|recording|python)" | grep -v grep
```

## 🔄 服务重启操作

### 完整系统重启
```bash
# 1. 停止所有服务
cd /data/MedicalRecord
./scripts/start-srs.sh stop
cd recording_service && ./start_recording_service.sh stop
pkill -f "funasr_api_server.py"

# 2. 等待进程完全结束
sleep 3

# 3. 按顺序重新启动
./scripts/start-srs.sh start
cd recording_service && ./start_recording_service.sh start
```

### 单个服务重启
```bash
# 重启SRS
./scripts/start-srs.sh restart

# 重启录制服务
cd recording_service && ./start_recording_service.sh restart
```

## 🔍 服务状态检查

### 综合状态检查脚本
```bash
#!/bin/bash
echo "=== 医疗录制系统服务状态检查 ==="

echo "1. SRS流媒体服务器状态:"
./scripts/start-srs.sh status

echo "2. 录制服务状态:"
cd recording_service && ./start_recording_service.sh status && cd ..

echo "3. 端口监听状态:"
ss -tuln | grep -E ":(1985|8080|8000|5410|5412)" | sort

echo "4. 相关进程:"
ps aux | grep -E "(srs|recording|python)" | grep -v grep

echo "5. 健康检查API:"
echo "SRS API: $(curl -s http://**************:1985/api/v1/summaries | jq -r '.data.ok // "ERROR"')"
echo "录制服务: $(curl -s http://**************:5410/api/health | jq -r '.message // "ERROR"')"
```

### 快速状态检查命令
```bash
# 检查所有服务端口
ss -tuln | grep -E ":(1985|8080|8000|5410|5412)" | sort

# 检查所有相关进程
ps aux | grep -E "(srs|recording)" | grep -v grep

# 健康检查API测试
curl -s http://**************:1985/api/v1/summaries && echo
curl -s http://**************:5410/api/health && echo
```

## 🔧 常见问题排查

### 1. SRS服务启动失败

#### 问题症状
- 端口无法监听
- API无响应
- 进程启动后立即退出

#### 排查步骤
```bash
# 检查端口占用
ss -tuln | grep -E ":(1985|8080|8000)"

# 查看SRS日志
tail -f /data/MedicalRecord/logs/srs/srs-console.log

# 检查配置文件
cat /data/MedicalRecord/srs/trunk/conf/rtc.conf

# 手动启动测试
cd /data/MedicalRecord/srs/trunk
./objs/srs -c conf/rtc.conf
```

#### 常见解决方案
- 端口被占用：`sudo lsof -i :1985` 查找占用进程并终止
- 配置文件错误：检查配置文件语法
- 权限问题：确保有足够的文件和网络权限

#### 配置文件问题诊断
```bash
# 确认当前使用的配置文件
ps aux | grep srs | grep -v grep

# 检查启动脚本配置
grep CONFIG_FILE /data/MedicalRecord/scripts/start-srs.sh

# 验证配置文件语法
cd /data/MedicalRecord/srs/trunk
./objs/srs -t -c conf/rtc.conf
```

### 2. 录制服务启动失败

#### 问题症状
- 端口5410无法监听
- 健康检查API无响应
- Python环境错误

#### 排查步骤
```bash
# 检查conda环境
echo $CONDA_DEFAULT_ENV
conda list | grep -E "(flask|requests)"

# 检查FFmpeg
ffmpeg -version

# 查看录制服务日志
tail -f /data/MedicalRecord/logs/recording/recording_service.log

# 手动启动测试
cd /data/MedicalRecord/recording_service
python recording_server.py
```

#### 常见解决方案
- conda环境未激活：`conda activate funasr`
- Python依赖缺失：`pip install flask flask-cors requests`
- FFmpeg未安装：`sudo apt install ffmpeg`
- 端口被占用：`sudo lsof -i :5410` 查找占用进程

### 3. 录制功能异常

#### 问题症状
- 录制开始失败
- 文件无法生成
- FFmpeg进程异常

#### 排查步骤
```bash
# 检查推流状态
curl http://**************:1985/api/v1/streams

# 检查录制目录权限
ls -la /data/MedicalRecord/recording_ori/

# 手动测试FFmpeg
ffmpeg -i http://**************:8080/live/test.flv -t 10 test.mp4

# 查看任务状态
curl http://**************:5410/api/tasks/status
```

#### 常见解决方案
- 推流未开始：确保WebRTC推流正常
- 目录权限问题：`chmod 755 /data/MedicalRecord/recording_ori/`
- FFmpeg参数错误：检查录制服务中的FFmpeg命令
- 磁盘空间不足：`df -h` 检查磁盘空间

### 4. SRS配置文件问题

#### 问题症状
- 启动脚本显示的端口与实际监听端口不一致
- 配置文件路径错误
- WebRTC连接失败

#### 排查步骤
```bash
# 检查实际使用的配置文件
ps aux | grep srs | grep -c

# 检查启动脚本配置
cat scripts/start-srs.sh | grep CONFIG_FILE

# 对比配置文件内容
diff srs/trunk/conf/rtc.conf srs/trunk/conf/simple-webrtc.conf

# 验证配置文件语法
cd srs/trunk && ./objs/srs -t -c conf/rtc.conf
```

#### 常见解决方案
- 配置文件路径不一致：修正启动脚本中的CONFIG_FILE变量
- 端口配置错误：检查配置文件中的端口设置
- WebRTC候选地址问题：设置正确的CANDIDATE环境变量

### 5. SRS WebRTC连接问题

#### 问题症状
- WebRTC推流失败
- 无法建立P2P连接
- 音视频传输中断

#### 排查步骤
```bash
# 检查WebRTC端口
ss -uln | grep 8000

# 检查候选地址配置
echo $CANDIDATE

# 测试STUN/TURN连接
curl http://**************:1985/api/v1/summaries

# 查看WebRTC相关日志
grep -i "webrtc\|rtc\|candidate" /data/MedicalRecord/logs/srs/srs.log | tail -20
```

#### 常见解决方案
- 防火墙阻止UDP端口：开放8000端口
- 候选地址配置错误：设置正确的外网IP
- NAT穿透问题：配置STUN/TURN服务器

### 6. 网络连接问题

#### 排查步骤
```bash
# 检查防火墙状态
sudo ufw status

# 检查网络连接
telnet ************** 5410
telnet ************** 1985

# 检查路由
ip route show

# 检查DNS解析
nslookup **************

# 检查网络接口
ip addr show
```

## 📊 服务监控建议

### 日志文件位置
- SRS日志: `/data/MedicalRecord/logs/srs/srs-console.log`
- 录制服务日志: `/data/MedicalRecord/logs/recording/recording_service.log`
- SRS API日志: `/data/MedicalRecord/logs/srs_api.log`

### 监控脚本示例
```bash
#!/bin/bash
# 服务监控脚本
LOGFILE="/data/MedicalRecord/logs/service_monitor.log"

while true; do
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 检查SRS
    if curl -s http://**************:1985/api/v1/summaries > /dev/null; then
        srs_status="OK"
    else
        srs_status="ERROR"
    fi
    
    # 检查录制服务
    if curl -s http://**************:5410/api/health > /dev/null; then
        recording_status="OK"
    else
        recording_status="ERROR"
    fi
    
    echo "$timestamp - SRS: $srs_status, Recording: $recording_status" >> $LOGFILE
    
    sleep 60
done
```

## 🔍 SRS配置确认与安全重启指南

### SRS配置文件确认

#### 当前配置文件检查
```bash
# 方法1：通过进程参数确认
ps aux | grep srs | grep -v grep

# 方法2：通过启动脚本确认
grep CONFIG_FILE /data/MedicalRecord/scripts/start-srs.sh

# 方法3：通过日志确认
grep "config.*-c" /data/MedicalRecord/logs/srs/srs.log | tail -1
```

#### 实际使用的配置文件
- **配置文件路径**: `/data/MedicalRecord/srs/trunk/conf/rtc.conf`
- **启动命令**: `./objs/srs -c conf/rtc.conf`
- **端口配置**:
  - HTTP API: 1985 (TCP)
  - HTTP-FLV: 8080 (TCP)
  - WebRTC: 8000 (UDP)
  - RTMP: 1935 (TCP)

#### 配置文件内容验证
```bash
# 查看当前配置文件
cat /data/MedicalRecord/srs/trunk/conf/rtc.conf

# 验证配置文件语法
cd /data/MedicalRecord/srs/trunk
./objs/srs -t -c conf/rtc.conf
```

### SRS安全重启流程

#### 重启前检查清单
```bash
# 1. 检查录制任务状态
curl -s http://**************:5410/api/tasks/status 2>/dev/null || echo "录制服务未运行"

# 2. 检查当前SRS状态
./scripts/start-srs.sh status

# 3. 检查端口占用
ss -tuln | grep -E ":(1985|8080|8000)"

# 4. 备份当前配置（可选）
cp srs/trunk/conf/rtc.conf srs/trunk/conf/rtc.conf.backup.$(date +%Y%m%d_%H%M%S)
```

#### 安全重启步骤
```bash
# 第一步：停止SRS服务器
cd /data/MedicalRecord
./scripts/start-srs.sh stop

# 第二步：等待进程完全停止
sleep 3

# 第三步：验证进程已停止
ps aux | grep srs | grep -v grep

# 第四步：重新启动SRS服务器
./scripts/start-srs.sh start

# 第五步：验证启动成功
./scripts/start-srs.sh status
```

#### 重启后验证检查
```bash
# 1. 检查进程状态
ps aux | grep srs | grep -v grep

# 2. 检查端口监听
ss -tuln | grep -E ":(1985|8080|8000)" | sort

# 3. 检查API响应
curl -s http://**************:1985/api/v1/summaries | jq '.data.ok'

# 4. 查看最新日志
tail -10 /data/MedicalRecord/logs/srs/srs-console.log

# 5. 测试WebRTC功能
curl -s http://**************:1985/api/v1/streams
```

#### 紧急重启流程（有录制任务时）
```bash
# 1. 查看活跃录制任务
curl http://**************:5410/api/tasks/status

# 2. 等待录制任务完成或手动停止
curl -X POST http://**************:5410/api/recording/video/stop \
  -H "Content-Type: application/json" \
  -d '{"task_id": "TASK_ID"}'

# 3. 确认所有录制任务已停止
curl http://**************:5410/api/tasks/status

# 4. 执行SRS重启
./scripts/start-srs.sh restart

# 5. 验证重启成功
curl http://**************:1985/api/v1/summaries
```

### 配置文件管理

#### 配置文件备份
```bash
# 创建配置备份
mkdir -p /data/MedicalRecord/backup/srs-config
cp srs/trunk/conf/rtc.conf backup/srs-config/rtc.conf.$(date +%Y%m%d_%H%M%S)

# 查看备份历史
ls -la backup/srs-config/
```

#### 配置文件恢复
```bash
# 恢复配置文件
cp backup/srs-config/rtc.conf.YYYYMMDD_HHMMSS srs/trunk/conf/rtc.conf

# 验证配置文件
cd srs/trunk && ./objs/srs -t -c conf/rtc.conf

# 重启服务应用配置
./scripts/start-srs.sh restart
```

## 🧪 功能测试指南

### 录制功能完整测试流程

#### 1. 环境准备测试
```bash
# 检查所有服务状态
cd /data/MedicalRecord
./scripts/start-srs.sh status
cd recording_service && ./start_recording_service.sh status

# 检查推流环境
curl http://**************:1985/api/v1/summaries
```

#### 2. 视频录制测试
```bash
# 开始视频录制
curl -X POST http://**************:5410/api/recording/video/start \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "TEST_VIDEO_001",
    "stream_key": "test",
    "userid": "test_user",
    "username": "测试用户",
    "chief_complaint": "功能测试",
    "present_illness": "测试视频录制功能",
    "past_medical_history": "无",
    "allergic_history": "无"
  }'

# 等待10秒后停止录制
sleep 10

# 停止视频录制
curl -X POST http://**************:5410/api/recording/video/stop \
  -H "Content-Type: application/json" \
  -d '{"task_id": "TEST_VIDEO_001"}'

# 查询录制状态
curl http://**************:5410/api/task/status/TEST_VIDEO_001
```

#### 3. 音频录制测试
```bash
# 开始音频录制
curl -X POST http://**************:5410/api/recording/audio/start \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "TEST_AUDIO_001",
    "stream_key": "test",
    "userid": "test_user",
    "username": "测试用户",
    "chief_complaint": "功能测试",
    "present_illness": "测试音频录制功能"
  }'

# 等待10秒后停止录制
sleep 10

# 停止音频录制
curl -X POST http://**************:5410/api/recording/audio/stop \
  -H "Content-Type: application/json" \
  -d '{"task_id": "TEST_AUDIO_001"}'

# 查询录制状态
curl http://**************:5410/api/task/status/TEST_AUDIO_001
```

#### 4. 验证录制文件
```bash
# 检查视频文件
ls -la /data/MedicalRecord/recording_ori/video/test/

# 检查音频文件
ls -la /data/MedicalRecord/recording_ori/audio/test/

# 验证文件完整性
ffprobe /data/MedicalRecord/recording_ori/video/test/*.mp4
ffprobe /data/MedicalRecord/recording_ori/audio/test/*.wav
```

## 📚 API接口快速参考

### SRS流媒体服务器接口

| 接口 | 方法 | 功能 | 示例 |
|------|------|------|------|
| `/api/v1/summaries` | GET | 服务器状态摘要 | `curl http://**************:1985/api/v1/summaries` |
| `/api/v1/streams` | GET | 当前流信息 | `curl http://**************:1985/api/v1/streams` |
| `/api/v1/clients` | GET | 客户端连接信息 | `curl http://**************:1985/api/v1/clients` |
| `/live/{stream}.flv` | GET | HTTP-FLV拉流 | `http://**************:8080/live/test.flv` |

### 录制服务接口

| 接口 | 方法 | 功能 | 示例 |
|------|------|------|------|
| `/api/recording/video/start` | POST | 开始视频录制 | `curl -X POST ...` |
| `/api/recording/video/stop` | POST | 停止视频录制 | `curl -X POST ...` |
| `/api/recording/audio/start` | POST | 开始音频录制 | `curl -X POST ...` |
| `/api/recording/audio/stop` | POST | 停止音频录制 | `curl -X POST ...` |

### 状态查询接口

| 接口 | 方法 | 功能 | 示例 |
|------|------|------|------|
| `/api/task/status/{task_id}` | GET | 查询单个任务状态 | `curl http://**************:5410/api/task/status/TASK_ID` |
| `/api/tasks/status` | GET | 查询所有任务状态 | `curl http://**************:5410/api/tasks/status` |
| `/api/health` | GET | 录制服务健康检查 | `curl http://**************:5410/api/health` |

### SRS管理命令快速参考

#### 基本操作
```bash
# SRS服务管理
./scripts/start-srs.sh start    # 启动SRS
./scripts/start-srs.sh stop     # 停止SRS
./scripts/start-srs.sh restart  # 重启SRS
./scripts/start-srs.sh status   # 查看状态

# 录制服务管理
cd recording_service
./start_recording_service.sh start    # 启动录制服务
./start_recording_service.sh stop     # 停止录制服务
./start_recording_service.sh restart  # 重启录制服务
./start_recording_service.sh status   # 查看状态
```

#### 快速检查命令
```bash
# 检查所有服务状态
ss -tuln | grep -E ":(1985|8080|8000|5410)" | sort

# 检查SRS API
curl -s http://**************:1985/api/v1/summaries | jq '.data.ok'

# 检查录制服务
curl -s http://**************:5410/api/health | jq '.message'

# 查看活跃流
curl -s http://**************:1985/api/v1/streams | jq '.streams | length'

# 查看录制任务
curl -s http://**************:5410/api/tasks/status | jq '.data.total_tasks'
```

### 请求参数模板

#### 录制开始请求
```json
{
  "task_id": "TASK_ID",
  "stream_key": "STREAM_KEY",
  "userid": "USER_ID",
  "username": "用户姓名",
  "chief_complaint": "主诉",
  "present_illness": "现病史",
  "past_medical_history": "既往史",
  "allergic_history": "过敏史",
  "metadata": {
    "patient_name": "患者姓名",
    "doctor_name": "医生姓名",
    "department_name": "科室名称",
    "recording_type": "video|audio"
  }
}
```

#### 录制停止请求
```json
{
  "task_id": "TASK_ID"
}
```

## 🔐 安全配置建议

### 网络安全
```bash
# 配置防火墙规则
sudo ufw allow 1985/tcp  # SRS API
sudo ufw allow 8080/tcp  # HTTP-FLV
sudo ufw allow 8000/udp  # WebRTC
sudo ufw allow 5410/tcp  # 录制服务

# 限制访问来源（可选）
sudo ufw allow from 192.168.1.0/24 to any port 5410
```

### 文件权限
```bash
# 设置录制目录权限
chmod 755 /data/MedicalRecord/recording_ori/
chmod 755 /data/MedicalRecord/recordings/temp/
chmod 755 /data/MedicalRecord/logs/

# 设置日志目录权限
chmod 644 /data/MedicalRecord/logs/recording/*.log
chmod 644 /data/MedicalRecord/logs/srs/*.log
```

### 服务用户配置
```bash
# 创建专用服务用户（推荐）
sudo useradd -r -s /bin/false medical-record
sudo chown -R medical-record:medical-record /data/MedicalRecord/
```

## 📈 性能优化建议

### 系统资源监控
```bash
# 监控CPU和内存使用
top -p $(pgrep -d',' -f 'srs|recording')

# 监控磁盘I/O
iotop -p $(pgrep -d',' -f 'srs|recording')

# 监控网络连接
ss -tuln | grep -E ":(1985|8080|8000|5410)"
```

### 录制性能优化
```bash
# 调整FFmpeg参数（在配置文件中）
# 降低CPU使用：使用硬件加速
# 减少磁盘I/O：调整缓冲区大小
# 优化网络：调整连接超时参数
```

### 存储空间管理
```bash
# 定期清理临时文件
find /data/MedicalRecord/recordings/temp/ -name "*.tmp" -mtime +1 -delete

# 压缩旧日志文件
gzip /data/MedicalRecord/logs/*/*.log.1

# 监控磁盘空间
df -h /data/MedicalRecord/
```

## 🚨 应急处理流程

### 服务异常处理
1. **立即响应**：停止异常服务，防止数据损坏
2. **问题诊断**：查看日志文件，确定问题原因
3. **快速恢复**：重启服务或切换到备用方案
4. **数据验证**：检查录制文件完整性
5. **问题记录**：记录问题和解决方案

### 数据恢复流程
```bash
# 检查临时文件
ls -la /data/MedicalRecord/recordings/temp/

# 恢复未完成的录制文件
for file in /data/MedicalRecord/recordings/temp/*.mp4; do
    if [ -f "$file" ]; then
        # 验证文件完整性
        ffprobe "$file" 2>/dev/null && echo "文件完整: $file"
    fi
done
```

### 紧急联系信息
- **技术支持**: 开发团队
- **系统管理员**: 运维团队
- **文档更新**: 请及时更新本文档

---

**文档版本**: v1.3
**创建时间**: 2025-08-08
**最后更新**: 2025-08-14
**适用系统**: 医疗录制系统 v2.1
**维护人员**: 开发团队

**更新日志**:
- v1.3 (2025-08-14): 简化系统架构，集成自动语音转文字功能
  - 删除SRS API回调服务相关内容（已弃用）
  - 在录制服务中集成FunASR自动调用功能
  - 更新服务架构为：SRS → 录制服务 → FunASR
  - 优化端口配置，使用5412端口用于FunASR API服务
  - 彻底清理HTTP静态文件服务器相关内容
- v1.2 (2025-08-14): 优化服务架构和SRS API回调服务说明
  - 删除HTTP静态文件服务器相关内容（外部客户端推流）
  - 详细说明SRS API回调服务的作用和启动条件
  - 更新服务依赖关系图和启动顺序
  - 优化端口检查命令，移除8888端口
- v1.1 (2025-08-14): 新增SRS配置确认与安全重启指南
  - 添加SRS配置文件确认方法
  - 详细的SRS安全重启流程
  - SRS故障排查和WebRTC连接问题解决
  - 扩展API接口快速参考，包含SRS管理命令
  - 配置文件管理和备份恢复流程
- v1.0 (2025-08-08): 初始版本，包含基础服务管理操作

**重要提醒**:
- 所有操作前请确保数据备份
- 重启SRS前必须检查录制任务状态
- 生产环境操作需要审批流程
- 定期检查和更新本操作指南
- SRS配置文件修改后需要重启服务生效
