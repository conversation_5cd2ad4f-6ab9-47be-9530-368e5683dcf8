# SRS 6.0 WebRTC流媒体服务器部署指南

## 📋 概述

本指南基于已验证可用的SRS 6.0配置，详细说明如何在新服务器上部署WebRTC流媒体服务。本文档基于实际生产环境配置，确保部署的可靠性和稳定性。

## 🎯 部署目标

- 部署SRS 6.0流媒体服务器，支持WebRTC推流
- 配置HTTP API服务（端口1985）
- 配置WebRTC UDP服务（端口8000）
- 配置HTTP文件服务（端口8080）
- 支持多路并发WebRTC推流
- 提供HTTP-FLV流输出供录制服务使用

## 📦 系统要求

### 硬件要求
- **CPU**: 4核心以上，推荐8核心
- **内存**: 8GB以上，推荐16GB
- **存储**: 50GB以上可用空间
- **网络**: 100Mbps以上带宽，支持UDP传输

### 软件要求
- **操作系统**: Ubuntu 18.04+ / CentOS 7+ / Debian 9+
- **编译器**: GCC 7.0+ 或 Clang 6.0+
- **依赖库**: OpenSSL 1.1+, libsrtp2, libopus

## 🚀 服务器迁移部署流程

### 步骤1：环境准备

#### Ubuntu/Debian系统
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装编译依赖
sudo apt install -y build-essential git cmake pkg-config
sudo apt install -y libssl-dev libopus-dev libsrtp2-dev
sudo apt install -y autoconf automake libtool
sudo apt install -y curl wget unzip net-tools

# 安装Python（用于HTTP服务器）
sudo apt install -y python3 python3-pip
```

#### CentOS/RHEL系统
```bash
# 更新系统包
sudo yum update -y

# 安装编译依赖
sudo yum groupinstall -y "Development Tools"
sudo yum install -y git cmake pkgconfig
sudo yum install -y openssl-devel opus-devel libsrtp-devel
sudo yum install -y autoconf automake libtool
sudo yum install -y curl wget unzip net-tools

# 安装Python
sudo yum install -y python3 python3-pip
```

### 步骤2：迁移项目文件

#### 方法1：直接复制（推荐）
```bash
# 在源服务器上打包
cd /data
tar -czf MedicalRecord.tar.gz MedicalRecord/

# 传输到新服务器
scp MedicalRecord.tar.gz user@new-server:/data/

# 在新服务器上解压
cd /data
tar -xzf MedicalRecord.tar.gz
```

#### 方法2：Git克隆（如果使用版本控制）
```bash
# 创建工作目录
sudo mkdir -p /data
cd /data

# 克隆项目（如果使用Git）
git clone https://gitee.com/ossrs/srs
# 或复制现有的srs目录
```

### 步骤3：SRS编译配置

```bash
# 进入SRS目录
cd /data/MedicalRecord/srs/trunk

# 检查现有编译状态
ls -la objs/

# 如果需要重新编译（推荐在新环境中重新编译）
make clean

# 配置编译选项
./configure \
    --rtc=on \
    --srt=on \
    --gb28181=on \
    --ssl=on \
    --https=on \
    --http-api=on \
    --http-server=on \
    --http-callback=on \
    --dvr=on \
    --hls=on

# 查看配置摘要
cat objs/_srs_build_summary.sh

# 开始编译（使用多核心加速）
make -j$(nproc)

# 验证编译结果
ls -la objs/srs
./objs/srs -v
```

### 步骤4：网络和防火墙配置

#### Ubuntu/Debian (ufw)
```bash
# 开放SRS必要端口
sudo ufw allow 1985/tcp   # HTTP API
sudo ufw allow 8000/udp   # WebRTC UDP
sudo ufw allow 8080/tcp   # HTTP服务器

# 查看防火墙状态
sudo ufw status

# 如果防火墙未启用，可以启用
sudo ufw enable
```

#### CentOS/RHEL (firewalld)
```bash
# 开放端口
sudo firewall-cmd --permanent --add-port=1985/tcp
sudo firewall-cmd --permanent --add-port=8000/udp
sudo firewall-cmd --permanent --add-port=8080/tcp

# 重载配置
sudo firewall-cmd --reload

# 查看开放端口
sudo firewall-cmd --list-ports
```

#### 直接使用iptables
```bash
# 开放端口
sudo iptables -A INPUT -p tcp --dport 1985 -j ACCEPT
sudo iptables -A INPUT -p udp --dport 8000 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 8080 -j ACCEPT

# 保存规则（Ubuntu/Debian）
sudo iptables-save > /etc/iptables/rules.v4

# 保存规则（CentOS/RHEL）
sudo service iptables save
```

### 步骤5：候选IP地址配置

#### 获取服务器IP地址
```bash
# 获取外网IP
EXTERNAL_IP=$(curl -s http://checkip.amazonaws.com 2>/dev/null || curl -s http://ipinfo.io/ip 2>/dev/null)
echo "外网IP: $EXTERNAL_IP"

# 获取内网IP
INTERNAL_IP=$(ip route get ******* | awk '{print $7; exit}' 2>/dev/null || hostname -I | awk '{print $1}')
echo "内网IP: $INTERNAL_IP"

# 选择合适的IP作为CANDIDATE
# 如果有外网IP，使用外网IP；否则使用内网IP
CANDIDATE_IP=${EXTERNAL_IP:-$INTERNAL_IP}
echo "WebRTC候选IP: $CANDIDATE_IP"
```

#### 设置环境变量
```bash
# 临时设置（当前会话）
export CANDIDATE=$CANDIDATE_IP

# 永久设置（添加到profile）
echo "export CANDIDATE=$CANDIDATE_IP" >> ~/.bashrc
source ~/.bashrc

# 验证设置
echo "CANDIDATE: $CANDIDATE"
```

### 步骤6：启动SRS服务

#### 使用官方rtc.conf配置启动
```bash
# 进入SRS目录
cd /data/MedicalRecord/srs/trunk

# 设置候选IP并启动
export CANDIDATE=$CANDIDATE_IP
./objs/srs -c conf/rtc.conf

# 或者后台启动
nohup ./objs/srs -c conf/rtc.conf > /data/MedicalRecord/logs/srs/srs-console.log 2>&1 &
```

#### 创建启动脚本
```bash
# 创建启动脚本
cat > /data/MedicalRecord/scripts/start-srs-production.sh << 'EOF'
#!/bin/bash

# SRS生产环境启动脚本
WORK_DIR="/data/MedicalRecord"
SRS_DIR="$WORK_DIR/srs/trunk"
LOG_DIR="$WORK_DIR/logs/srs"
PID_FILE="$LOG_DIR/srs.pid"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 获取服务器IP
EXTERNAL_IP=$(curl -s --connect-timeout 5 http://checkip.amazonaws.com 2>/dev/null || curl -s --connect-timeout 5 http://ipinfo.io/ip 2>/dev/null)
INTERNAL_IP=$(ip route get ******* | awk '{print $7; exit}' 2>/dev/null || hostname -I | awk '{print $1}')
CANDIDATE_IP=${EXTERNAL_IP:-$INTERNAL_IP}

echo "启动SRS服务器..."
echo "候选IP地址: $CANDIDATE_IP"

# 设置环境变量并启动
cd "$SRS_DIR"
export CANDIDATE="$CANDIDATE_IP"

# 启动SRS
nohup ./objs/srs -c conf/rtc.conf > "$LOG_DIR/srs-console.log" 2>&1 &
SRS_PID=$!

# 保存PID
echo $SRS_PID > "$PID_FILE"

echo "SRS服务器已启动，PID: $SRS_PID"
echo "HTTP API: http://$CANDIDATE_IP:1985"
echo "WebRTC推流: webrtc://$CANDIDATE_IP:8000/live/streamkey"
echo "HTTP服务器: http://$CANDIDATE_IP:8080"
EOF

chmod +x /data/MedicalRecord/scripts/start-srs-production.sh
```

### 步骤7：服务验证和测试

#### 基础连通性测试
```bash
# 等待服务启动
sleep 5

# 测试HTTP API
curl -s http://localhost:1985/api/v1/summaries

# 测试端口监听
ss -tuln | grep -E ":(1985|8000|8080)"

# 测试外网访问（替换为实际IP）
curl -s http://$CANDIDATE_IP:1985/api/v1/summaries
```

#### WebRTC API测试
```bash
# 测试WebRTC publish API
curl -s http://localhost:1985/rtc/v1/publish/ \
  -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "api": "http://localhost:1985/rtc/v1/publish/",
    "tid": "test123",
    "streamurl": "webrtc://localhost:8000/live/test",
    "clientip": null,
    "sdp": "v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"
  }'
```

#### 外部WebRTC客户端验证
```bash
# 使用外部客户端连接WebRTC推流地址
echo "WebRTC推流地址: webrtc://$CANDIDATE_IP:1985/rtc/v1/whip/?app=live&stream=test"
echo "SRS API地址: http://$CANDIDATE_IP:1985"
echo "HTTP-FLV拉流地址: http://$CANDIDATE_IP:8080/live/test.flv"
```

## 🔧 配置文件详细说明

### 使用的配置文件：conf/rtc.conf

基于我们的实际生产配置，以下是完整的配置文件内容和说明：

```nginx
# SRS WebRTC流媒体服务器配置
# 版本: SRS 6.0
# 用途: 医疗电子病历WebRTC推流和录制系统

listen              1935;           # RTMP监听端口
max_connections     1000;           # 最大并发连接数
daemon              off;            # 前台运行（便于调试）
srs_log_tank        console;        # 日志输出到控制台

# HTTP服务器配置 - 提供静态文件和HTTP-FLV服务
http_server {
    enabled         on;             # 启用HTTP服务器
    listen          8080;           # HTTP服务端口
    dir             ./objs/nginx/html;  # 静态文件目录
}

# HTTP API配置 - 提供管理接口
http_api {
    enabled         on;             # 启用HTTP API
    listen          1985;           # API服务端口
}

# 统计配置
stats {
    network         0;              # 网络统计
}

# WebRTC服务器配置 - 核心推流接收
rtc_server {
    enabled         on;             # 启用WebRTC服务
    listen          8000;           # WebRTC信令和媒体端口（UDP）
    candidate       $CANDIDATE;     # 外部访问IP（环境变量）
}

# 虚拟主机配置 - 关键业务逻辑
vhost __defaultVhost__ {
    rtc {
        enabled         on;         # 启用WebRTC功能
        rtmp_to_rtc     off;       # 禁用RTMP到WebRTC转换
        rtc_to_rtmp     on;        # ⭐ 启用WebRTC到RTMP转换（录制关键配置）
    }
    http_remux {
        enabled         on;         # 启用HTTP-FLV输出
        mount           [vhost]/[app]/[stream].flv;  # FLV流访问路径
    }
}
```

### 关键配置项详解

#### 1. 端口配置说明
| 端口 | 协议 | 用途 | 访问示例 |
|------|------|------|----------|
| 1985 | HTTP | API管理接口 | `http://**************:1985/api/v1/versions` |
| 8080 | HTTP | HTTP-FLV流输出 | `http://**************:8080/live/test.flv` |
| 8000 | UDP/TCP | WebRTC信令和媒体 | `webrtc://**************:8000/live/test` |
| 1935 | TCP | RTMP流输出 | `rtmp://**************:1935/live/test` |

#### 2. rtc_to_rtmp配置的重要性

**配置项**: `rtc_to_rtmp on`

**作用**:
- 将WebRTC推流转换为RTMP格式
- 使录制服务能够通过RTMP协议拉取流
- 同时生成HTTP-FLV流供录制服务使用

**为什么重要**:
```bash
# 没有此配置时：
WebRTC推流 → SRS → ❌ 无法录制（协议不兼容）

# 有此配置时：
WebRTC推流 → SRS → RTMP/HTTP-FLV → 录制服务 → MP4文件
```

#### 3. 环境变量配置

**CANDIDATE变量**:
```bash
export CANDIDATE=**************  # 服务器外部IP
```

**作用**:
- WebRTC需要知道服务器的外部IP地址
- 用于ICE候选地址生成
- 确保客户端能正确连接到服务器

### 启动命令和环境变量设置

#### 标准启动命令
```bash
# 进入SRS目录
cd /data/MedicalRecord/srs/trunk

# 设置候选IP并启动
export CANDIDATE=**************
./objs/srs -c conf/rtc.conf
```

#### 后台启动命令
```bash
# 后台启动并记录日志
nohup ./objs/srs -c conf/rtc.conf > /data/MedicalRecord/logs/srs/srs-console.log 2>&1 &
```

### 配置验证方法

#### 1. 验证SRS服务启动
```bash
# 检查SRS进程
ps aux | grep srs

# 检查端口监听
ss -tuln | grep -E ":(1985|8080|8000|1935)"

# 预期输出：
# tcp   LISTEN 0.0.0.0:1985  (HTTP API)
# tcp   LISTEN 0.0.0.0:8080  (HTTP服务器)
# udp   LISTEN 0.0.0.0:8000  (WebRTC)
# tcp   LISTEN 0.0.0.0:1935  (RTMP)
```

#### 2. 验证API接口
```bash
# 检查SRS版本信息
curl -s http://**************:1985/api/v1/versions | python -m json.tool

# 预期输出：
{
    "code": 0,
    "server": "vid-xxxxxx",
    "data": {
        "major": 6,
        "minor": 0,
        "revision": 134,
        "version": "6.0.134"
    }
}

# 检查当前流状态
curl -s http://**************:1985/api/v1/streams/ | python -m json.tool
```

#### 3. 验证WebRTC配置
```bash
# 检查WebRTC配置是否正确加载
curl -s http://**************:1985/api/v1/configs/ | grep -A 5 -B 5 "rtc_to_rtmp"

# 验证候选IP设置
echo "当前CANDIDATE: $CANDIDATE"
```

### 故障排除步骤

#### 问题1：rtc_to_rtmp配置未生效
**症状**: 录制服务无法拉取到流，提示"Input/output error"

**解决方案**:
```bash
# 1. 检查配置文件
grep -A 10 "vhost __defaultVhost__" /data/MedicalRecord/srs/trunk/conf/rtc.conf

# 2. 确认rtc_to_rtmp为on
grep "rtc_to_rtmp" /data/MedicalRecord/srs/trunk/conf/rtc.conf

# 3. 重启SRS服务
kill $(pgrep srs)
cd /data/MedicalRecord/srs/trunk
export CANDIDATE=**************
./objs/srs -c conf/rtc.conf
```

#### 问题2：WebRTC连接失败
**症状**: 浏览器无法建立WebRTC连接

**解决方案**:
```bash
# 1. 检查CANDIDATE设置
echo $CANDIDATE
# 应该输出服务器的外部IP

# 2. 检查防火墙
sudo ufw status
sudo ufw allow 8000/udp
sudo ufw allow 1985/tcp
sudo ufw allow 8080/tcp
sudo ufw allow 1935/tcp

# 3. 检查网络连通性
nc -u -v ************** 8000
```

#### 问题3：HTTP-FLV流无法访问
**症状**: 录制服务提示"HTTP-FLV源不可用"

**解决方案**:
```bash
# 1. 检查HTTP服务器状态
curl -I http://**************:8080/

# 2. 检查流是否存在
curl -s http://**************:1985/api/v1/streams/ | grep "test"

# 3. 手动测试HTTP-FLV流
curl -I http://**************:8080/live/test.flv
```

## 📊 端口配置总览

| 服务 | 端口 | 协议 | 用途 | 验证命令 |
|------|------|------|------|----------|
| HTTP API | 1985 | TCP | WebRTC API、管理接口 | `curl http://IP:1985/api/v1/versions` |
| WebRTC | 8000 | UDP | WebRTC媒体传输 | `nc -u -v IP 8000` |
| HTTP服务器 | 8080 | TCP | 静态文件、HTTP-FLV | `curl -I http://IP:8080/` |
| RTMP | 1935 | TCP | RTMP流输出 | `ffmpeg -i rtmp://IP:1935/live/test` |

## 🔍 故障排除

### 常见问题1：端口被占用
```bash
# 检查端口占用
ss -tuln | grep -E ":(1985|8000|8080)"

# 查找占用进程
sudo lsof -i :1985
sudo lsof -i :8000
sudo lsof -i :8080

# 停止占用进程
sudo kill -9 PID
```

### 常见问题2：防火墙阻止
```bash
# 检查防火墙状态
sudo ufw status
sudo firewall-cmd --list-ports

# 临时关闭防火墙测试
sudo ufw disable
sudo systemctl stop firewalld
```

### 常见问题3：WebRTC连接失败
```bash
# 检查候选IP设置
echo $CANDIDATE

# 检查SRS日志
tail -f /data/MedicalRecord/logs/srs/srs-console.log

# 测试网络连通性
ping $CANDIDATE_IP
telnet $CANDIDATE_IP 1985
```

### 常见问题4：编译失败
```bash
# 检查依赖
./configure --help

# 清理重新编译
make clean
./configure --rtc=on --srt=on --gb28181=on
make -j$(nproc)

# 检查编译日志
cat objs/srs_build.log
```

## 📈 性能监控

### 系统资源监控
```bash
# 查看SRS进程资源使用
top -p $(pgrep srs)

# 查看网络连接
ss -tuln | grep srs
netstat -i

# 查看磁盘使用
df -h
du -sh /data/MedicalRecord/
```

### SRS状态监控
```bash
# 查看服务器状态
curl -s http://localhost:1985/api/v1/summaries | jq

# 查看流信息
curl -s http://localhost:1985/api/v1/streams/ | jq

# 查看客户端信息
curl -s http://localhost:1985/api/v1/clients/ | jq
```

## 🔒 安全配置

### 基础安全设置
```bash
# 限制API访问（可选）
# 在配置文件中添加IP白名单

# 设置文件权限
chmod 755 /data/MedicalRecord/srs/trunk/objs/srs
chown -R $USER:$USER /data/MedicalRecord/

# 定期更新系统
sudo apt update && sudo apt upgrade -y
```

### 生产环境建议
1. **使用HTTPS**：配置SSL证书
2. **访问控制**：设置IP白名单
3. **监控告警**：配置服务监控
4. **日志轮转**：配置日志管理
5. **备份策略**：定期备份配置

## 📋 部署检查清单

### 部署前检查
- [ ] 服务器硬件资源充足
- [ ] 网络带宽满足要求
- [ ] 防火墙端口已开放
- [ ] 依赖软件已安装

### 部署后验证
- [ ] SRS服务正常启动
- [ ] HTTP API响应正常
- [ ] WebRTC API测试通过
- [ ] 端口监听状态正确
- [ ] 前端测试页面可访问
- [ ] WebRTC推流功能正常

### 生产环境配置
- [ ] 候选IP地址配置正确
- [ ] 服务自动启动配置
- [ ] 日志轮转配置
- [ ] 监控告警配置
- [ ] 备份策略实施

---

**文档版本**: v2.0 (基于实际部署验证)
**创建时间**: 2025-01-08
**适用版本**: SRS 6.0.134+
**验证环境**: Ubuntu 20.04, CentOS 8
**维护人员**: 开发团队