# 医疗电子病历WebRTC录制系统 - 项目结构说明

## 📁 项目目录结构

```
/data/MedicalRecord/
├── README.md                           # 项目说明文档
├── Windows-WebRTC-解决方案.md          # Windows环境解决方案
├── 项目完成状态报告.md                 # 项目完成状态
│
├── config/                             # 配置文件目录
│   └── config.yaml                     # 主配置文件
│
├── documents/                          # 项目文档
│   ├── 01-系统架构设计文档.md          # 系统架构设计
│   ├── 02-API接口详细规范.md           # API接口规范
│   ├── SRS部署指南.md                  # SRS服务器部署指南
│   ├── 前端WebRTC开发指南.md           # 前端开发指南
│   └── 项目结构说明.md                 # 本文档
│
├── srs/                                # SRS流媒体服务器
│   ├── trunk/                          # SRS主程序目录
│   │   ├── conf/rtc.conf              # WebRTC配置文件
│   │   ├── objs/srs                   # SRS可执行文件
│   │   └── objs/nginx/html/           # 静态文件目录
│   └── [其他SRS文件]
│
├── recording_service/                  # 录制服务模块
│   ├── recording_server.py            # 录制服务主程序
│   ├── config_loader.py               # 配置加载器
│   ├── file_manager.py                # 文件管理器
│   ├── config.py                      # 配置定义
│   ├── start_recording_service.sh     # 启动脚本
│   └── install_dependencies.sh        # 依赖安装脚本
│
├── funasr/                            # 语音识别服务
│   ├── app.py                         # FunASR主程序
│   ├── download_model.py              # 模型下载脚本
│   └── hotwords.txt                   # 热词文件
│
├── srs_api/                           # SRS API服务
│   └── medical_recording_server.py    # 医疗录制API服务器
│
├── web/                               # 前端文件
│   └── webrtc-test.html              # WebRTC测试页面
│
├── scripts/                           # 工具脚本
│   ├── start-srs.sh                  # SRS启动脚本
│   ├── test-webrtc.sh                # WebRTC测试脚本
│   └── webrtc-security-fix.sh        # WebRTC安全修复脚本
│
├── recording_ori/                     # 录制文件存储目录
│   ├── [stream_key]/                 # 按推流码分类的录制文件
│   └── ...
│
├── recordings/                        # 临时录制目录
│   └── temp/                         # 临时文件存储
│
└── logs/                             # 日志文件目录
    ├── recording/                    # 录制服务日志
    └── srs/                         # SRS服务日志
```

## 🔧 核心组件说明

### 1. SRS流媒体服务器 (端口配置)
- **WebRTC信令**: 端口8000 (UDP/TCP)
- **HTTP API**: 端口1985 (TCP)
- **HTTP-FLV**: 端口8080 (TCP)
- **RTMP**: 端口1935 (TCP)

### 2. 录制服务 (端口5410)
- **主程序**: `recording_service/recording_server.py`
- **API格式**: 医疗系统标准格式
- **功能**: WebRTC流录制、文件管理、状态监控

### 3. 语音识别服务
- **主程序**: `funasr/app.py`
- **功能**: 离线语音转文字、说话人分离

### 4. 前端WebRTC
- **测试页面**: `web/webrtc-test.html`
- **功能**: WebRTC推流、录制控制

## 🚀 服务启动顺序

### 1. 启动SRS服务器
```bash
cd /data/MedicalRecord/srs/trunk
export CANDIDATE=**************
./objs/srs -c conf/rtc.conf
```

### 2. 启动录制服务
```bash
cd /data/MedicalRecord/recording_service
python recording_server.py
```

### 3. 使用外部WebRTC客户端
```
使用Windows客户端或其他外部设备访问WebRTC推流地址：
webrtc://**************:1985/rtc/v1/whip/?app=live&stream=test
```

## 📋 关键配置文件

### SRS配置 (`srs/trunk/conf/rtc.conf`)
```nginx
# 关键配置项
rtc_to_rtmp on;  # 启用WebRTC到RTMP转换（录制必需）
candidate $CANDIDATE;  # 外部IP地址
```

### 录制服务配置 (`config/config.yaml`)
```yaml
# 服务器配置
server:
  host: "0.0.0.0"
  port: 5410

# SRS配置
srs:
  host: "**************"
  api_port: 1985
  http_port: 8080
```

## 🔍 故障排除

### 常见问题检查
1. **端口占用**: `ss -tuln | grep -E ":(1985|8080|8000|1935|5410)"`
2. **服务状态**: `ps aux | grep -E "(srs|recording_server)"`
3. **日志查看**: `tail -f logs/recording/recording_service.log`

### 服务验证
1. **SRS API**: `curl http://**************:1985/api/v1/versions`
2. **录制服务**: `curl http://**************:5410/api/health`
3. **WebRTC连接**: 访问测试页面进行推流测试

## 📊 系统监控

### 关键指标
- SRS连接数和流状态
- 录制服务活跃任务数
- 磁盘空间使用情况
- 网络带宽使用情况

### 日志位置
- SRS日志: 控制台输出或 `logs/srs/`
- 录制服务日志: `logs/recording/recording_service.log`
- 系统日志: `/var/log/syslog`

---

**文档版本**: v1.0
**创建时间**: 2025-01-08
**维护人员**: 系统管理团队
