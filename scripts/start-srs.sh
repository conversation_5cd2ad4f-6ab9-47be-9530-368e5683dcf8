#!/bin/bash

# 电子病历生成系统 - SRS 6.0 启动脚本
# 用于启动和管理SRS WebRTC推流服务器

# 设置工作目录
WORK_DIR="/data/MedicalRecord"
SRS_DIR="$WORK_DIR/srs/trunk"
LOG_DIR="$WORK_DIR/logs/srs"
CONFIG_FILE="$SRS_DIR/conf/rtc.conf"
PID_FILE="$LOG_DIR/srs.pid"

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查SRS是否已编译
check_srs_binary() {
    if [ ! -f "$SRS_DIR/objs/srs" ]; then
        log_error "SRS二进制文件不存在: $SRS_DIR/objs/srs"
        log_info "请先编译SRS："
        echo "  cd $SRS_DIR"
        echo "  ./configure --rtc=on --srt=on --gb28181=on"
        echo "  make"
        exit 1
    fi
    log_info "SRS二进制文件检查通过"
}

# 检查配置文件
check_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    log_info "配置文件检查通过: $CONFIG_FILE"
}

# 创建必要的目录
create_directories() {
    mkdir -p "$LOG_DIR"
    mkdir -p "$WORK_DIR/recordings"
    log_info "目录创建完成"
}

# 检查端口占用
check_ports() {
    local ports=(5400 5401 5402 5403)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if netstat -tuln | grep -q ":$port "; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        log_warn "以下端口已被占用: ${occupied_ports[*]}"
        log_warn "如果是之前的SRS进程，请先停止"
        read -p "是否继续启动？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log_info "端口检查通过，所有端口可用"
    fi
}

# 获取服务器IP地址
get_server_ip() {
    # 尝试获取外网IP
    EXTERNAL_IP=$(curl -s --connect-timeout 5 http://checkip.amazonaws.com 2>/dev/null || curl -s --connect-timeout 5 http://ipinfo.io/ip 2>/dev/null)
    
    # 获取内网IP
    INTERNAL_IP=$(ip route get ******* | awk '{print $7; exit}' 2>/dev/null || hostname -I | awk '{print $1}')
    
    if [ -n "$EXTERNAL_IP" ]; then
        CANDIDATE_IP="$EXTERNAL_IP"
        log_info "检测到外网IP: $EXTERNAL_IP"
    elif [ -n "$INTERNAL_IP" ]; then
        CANDIDATE_IP="$INTERNAL_IP"
        log_info "使用内网IP: $INTERNAL_IP"
    else
        CANDIDATE_IP="127.0.0.1"
        log_warn "无法检测IP地址，使用本地回环: $CANDIDATE_IP"
    fi
    
    export CANDIDATE="$CANDIDATE_IP"
    log_info "WebRTC候选地址设置为: $CANDIDATE"
}

# 启动SRS服务器
start_srs() {
    log_info "正在启动SRS WebRTC服务器..."
    
    cd "$SRS_DIR"
    
    # 设置环境变量
    export CANDIDATE="$CANDIDATE_IP"
    
    # 启动SRS
    nohup ./objs/srs -c "$CONFIG_FILE" > "$LOG_DIR/srs-console.log" 2>&1 &
    SRS_PID=$!
    
    # 保存PID
    echo $SRS_PID > "$PID_FILE"
    
    # 等待启动
    sleep 3
    
    # 检查进程是否启动成功
    if kill -0 $SRS_PID 2>/dev/null; then
        log_info "SRS服务器启动成功，PID: $SRS_PID"
        
        # 显示服务信息
        echo
        log_info "=== SRS WebRTC服务器信息 ==="
        echo "  WebRTC推流端口: 5400 (UDP)"
        echo "  RTMP端口: 5401"
        echo "  HTTP管理界面: http://$CANDIDATE_IP:5402"
        echo "  HTTP API: http://$CANDIDATE_IP:5403"
        echo "  日志文件: $LOG_DIR/srs.log"
        echo "  控制台日志: $LOG_DIR/srs-console.log"
        echo "  配置文件: $CONFIG_FILE"
        echo "  候选地址: $CANDIDATE"
        echo
        
        # 显示WebRTC推流地址示例
        log_info "=== WebRTC推流地址示例 ==="
        echo "  推流地址: webrtc://$CANDIDATE_IP:5403/rtc/v1/whip/?app=live&stream=test"
        echo "  HTTP-FLV拉流: http://$CANDIDATE_IP:5402/live/test.flv"
        echo
        
        # 显示API测试命令
        log_info "=== API测试命令 ==="
        echo "  查看服务器状态: curl http://$CANDIDATE_IP:5403/api/v1/summaries"
        echo "  查看流信息: curl http://$CANDIDATE_IP:5403/api/v1/streams"
        echo
        
        log_info "SRS服务器启动完成，可以开始WebRTC推流测试"
        
    else
        log_error "SRS服务器启动失败"
        log_error "请检查日志文件: $LOG_DIR/srs-console.log"
        exit 1
    fi
}

# 停止SRS服务器
stop_srs() {
    if [ -f "$PID_FILE" ]; then
        SRS_PID=$(cat "$PID_FILE")
        if kill -0 $SRS_PID 2>/dev/null; then
            log_info "正在停止SRS服务器 (PID: $SRS_PID)..."
            kill $SRS_PID
            
            # 等待进程结束
            local count=0
            while kill -0 $SRS_PID 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            if kill -0 $SRS_PID 2>/dev/null; then
                log_warn "进程未正常结束，强制终止..."
                kill -9 $SRS_PID
            fi
            
            rm -f "$PID_FILE"
            log_info "SRS服务器已停止"
        else
            log_warn "SRS进程不存在 (PID: $SRS_PID)"
            rm -f "$PID_FILE"
        fi
    else
        log_warn "PID文件不存在，尝试查找SRS进程..."
        pkill -f "objs/srs.*medical-webrtc.conf" && log_info "已停止SRS进程" || log_warn "未找到SRS进程"
    fi
}

# 查看SRS状态
status_srs() {
    if [ -f "$PID_FILE" ]; then
        SRS_PID=$(cat "$PID_FILE")
        if kill -0 $SRS_PID 2>/dev/null; then
            log_info "SRS服务器正在运行 (PID: $SRS_PID)"
            
            # 显示端口监听状态
            echo
            log_info "=== 端口监听状态 ==="
            netstat -tuln | grep -E ":(5400|5401|5402|5403) " || log_warn "未检测到SRS端口监听"
            
            # 显示最近的日志
            echo
            log_info "=== 最近日志 (最后10行) ==="
            if [ -f "$LOG_DIR/srs.log" ]; then
                tail -10 "$LOG_DIR/srs.log"
            else
                log_warn "日志文件不存在"
            fi
            
        else
            log_error "SRS服务器未运行 (PID文件存在但进程不存在)"
            rm -f "$PID_FILE"
        fi
    else
        log_warn "SRS服务器未运行 (PID文件不存在)"
    fi
}

# 重启SRS服务器
restart_srs() {
    log_info "正在重启SRS服务器..."
    stop_srs
    sleep 2
    start_srs
}

# 显示日志
show_logs() {
    local log_type=${1:-"srs"}
    
    case $log_type in
        "srs")
            if [ -f "$LOG_DIR/srs.log" ]; then
                log_info "显示SRS日志 (按Ctrl+C退出):"
                tail -f "$LOG_DIR/srs.log"
            else
                log_error "SRS日志文件不存在: $LOG_DIR/srs.log"
            fi
            ;;
        "console")
            if [ -f "$LOG_DIR/srs-console.log" ]; then
                log_info "显示控制台日志 (按Ctrl+C退出):"
                tail -f "$LOG_DIR/srs-console.log"
            else
                log_error "控制台日志文件不存在: $LOG_DIR/srs-console.log"
            fi
            ;;
        *)
            log_error "未知的日志类型: $log_type"
            echo "可用的日志类型: srs, console"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "电子病历生成系统 - SRS 6.0 管理脚本"
    echo
    echo "用法: $0 {start|stop|restart|status|logs|help}"
    echo
    echo "命令说明:"
    echo "  start    - 启动SRS WebRTC服务器"
    echo "  stop     - 停止SRS服务器"
    echo "  restart  - 重启SRS服务器"
    echo "  status   - 查看SRS运行状态"
    echo "  logs     - 显示SRS日志 (实时)"
    echo "  help     - 显示此帮助信息"
    echo
    echo "日志文件位置:"
    echo "  SRS日志: $LOG_DIR/srs.log"
    echo "  控制台日志: $LOG_DIR/srs-console.log"
    echo
    echo "配置文件: $CONFIG_FILE"
    echo
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            check_srs_binary
            check_config
            create_directories
            check_ports
            get_server_ip
            start_srs
            ;;
        "stop")
            stop_srs
            ;;
        "restart")
            check_srs_binary
            check_config
            create_directories
            get_server_ip
            restart_srs
            ;;
        "status")
            status_srs
            ;;
        "logs")
            show_logs "${2:-srs}"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
