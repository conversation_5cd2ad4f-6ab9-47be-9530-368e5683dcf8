#!/bin/bash

# WebRTC安全上下文问题解决方案脚本
# 支持多种解决方案，适用于不同环境
# 注意：此脚本仅用于开发和测试环境，生产环境请使用外部WebRTC客户端

WORK_DIR="/data/MedicalRecord"
HTML_FILE="$WORK_DIR/web/webrtc-test.html"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示主菜单
show_menu() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  WebRTC安全上下文问题解决方案${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    echo "请选择解决方案:"
    echo ""
    echo "1) 启动本地HTTP服务器 (推荐)"
    echo "2) 生成Chrome开发模式启动命令"
    echo "3) 检查当前环境状态"
    echo "4) 显示详细解决方案说明"
    echo "5) 退出"
    echo ""
    echo -n "请输入选项 (1-5): "
}

# 启动HTTP服务器
start_http_server() {
    log_step "启动本地HTTP服务器解决方案"
    echo ""
    
    # 检查可用端口
    local port=8080
    while ss -tuln | grep -q ":$port "; do
        port=$((port + 1))
    done
    
    log_info "将使用端口: $port"
    
    # 检查可用的HTTP服务器
    if command -v python3 &> /dev/null; then
        log_info "使用Python3启动HTTP服务器..."
        echo ""
        log_info "服务器启动后，请在浏览器中访问:"
        echo -e "${GREEN}  http://localhost:$port/web/webrtc-test.html${NC}"
        echo ""
        log_warn "按 Ctrl+C 停止服务器"
        echo ""
        
        cd "$WORK_DIR"
        python3 -m http.server $port
        
    elif command -v python &> /dev/null; then
        log_info "使用Python2启动HTTP服务器..."
        echo ""
        log_info "服务器启动后，请在浏览器中访问:"
        echo -e "${GREEN}  http://localhost:$port/web/webrtc-test.html${NC}"
        echo ""
        log_warn "按 Ctrl+C 停止服务器"
        echo ""
        
        cd "$WORK_DIR"
        python -m SimpleHTTPServer $port
        
    elif command -v npx &> /dev/null; then
        log_info "使用Node.js启动HTTP服务器..."
        echo ""
        log_info "服务器启动后，请在浏览器中访问:"
        echo -e "${GREEN}  http://localhost:$port/web/webrtc-test.html${NC}"
        echo ""
        log_warn "按 Ctrl+C 停止服务器"
        echo ""
        
        cd "$WORK_DIR"
        npx http-server -p $port --cors
        
    else
        log_error "未找到可用的HTTP服务器"
        echo ""
        echo "请安装以下工具之一:"
        echo "  - Python: sudo apt install python3"
        echo "  - Node.js: sudo apt install nodejs npm"
        echo ""
        read -p "按回车键返回主菜单..."
    fi
}

# 生成Chrome启动命令
generate_chrome_command() {
    log_step "生成Chrome开发模式启动命令"
    echo ""
    
    local os_type=$(uname -s)
    
    case $os_type in
        "Linux")
            log_info "Linux系统Chrome启动命令:"
            echo ""
            echo -e "${GREEN}google-chrome \\${NC}"
            echo -e "${GREEN}  --allow-file-access-from-files \\${NC}"
            echo -e "${GREEN}  --disable-web-security \\${NC}"
            echo -e "${GREEN}  --disable-features=VizDisplayCompositor \\${NC}"
            echo -e "${GREEN}  --allow-running-insecure-content \\${NC}"
            echo -e "${GREEN}  --ignore-certificate-errors \\${NC}"
            echo -e "${GREEN}  --user-data-dir=/tmp/chrome-webrtc-dev \\${NC}"
            echo -e "${GREEN}  file://$HTML_FILE${NC}"
            ;;
            
        "Darwin")
            log_info "macOS系统Chrome启动命令:"
            echo ""
            echo -e "${GREEN}/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome \\${NC}"
            echo -e "${GREEN}  --allow-file-access-from-files \\${NC}"
            echo -e "${GREEN}  --disable-web-security \\${NC}"
            echo -e "${GREEN}  --disable-features=VizDisplayCompositor \\${NC}"
            echo -e "${GREEN}  --allow-running-insecure-content \\${NC}"
            echo -e "${GREEN}  --ignore-certificate-errors \\${NC}"
            echo -e "${GREEN}  --user-data-dir=/tmp/chrome-webrtc-dev \\${NC}"
            echo -e "${GREEN}  file://$HTML_FILE${NC}"
            ;;
            
        *)
            log_info "通用Chrome启动命令:"
            echo ""
            echo -e "${GREEN}chrome \\${NC}"
            echo -e "${GREEN}  --allow-file-access-from-files \\${NC}"
            echo -e "${GREEN}  --disable-web-security \\${NC}"
            echo -e "${GREEN}  --disable-features=VizDisplayCompositor \\${NC}"
            echo -e "${GREEN}  --allow-running-insecure-content \\${NC}"
            echo -e "${GREEN}  --ignore-certificate-errors \\${NC}"
            echo -e "${GREEN}  --user-data-dir=/tmp/chrome-webrtc-dev \\${NC}"
            echo -e "${GREEN}  file://$HTML_FILE${NC}"
            ;;
    esac
    
    echo ""
    log_warn "Windows用户请使用提供的 chrome-webrtc-dev.bat 脚本"
    echo ""
    log_warn "安全提醒:"
    echo "  - 这些参数会禁用Chrome的安全特性"
    echo "  - 仅用于开发和测试"
    echo "  - 测试完成后请关闭Chrome"
    echo ""
    
    read -p "按回车键返回主菜单..."
}

# 检查环境状态
check_environment() {
    log_step "检查当前环境状态"
    echo ""
    
    # 检查操作系统
    log_info "操作系统: $(uname -s) $(uname -r)"
    
    # 检查浏览器
    echo ""
    log_info "浏览器检查:"
    if command -v google-chrome &> /dev/null; then
        echo "  ✅ Google Chrome: $(google-chrome --version 2>/dev/null || echo '已安装')"
    elif command -v chromium-browser &> /dev/null; then
        echo "  ✅ Chromium: $(chromium-browser --version 2>/dev/null || echo '已安装')"
    else
        echo "  ❌ 未找到Chrome/Chromium"
    fi
    
    if command -v firefox &> /dev/null; then
        echo "  ✅ Firefox: $(firefox --version 2>/dev/null || echo '已安装')"
    else
        echo "  ❌ 未找到Firefox"
    fi
    
    # 检查HTTP服务器工具
    echo ""
    log_info "HTTP服务器工具:"
    if command -v python3 &> /dev/null; then
        echo "  ✅ Python3: $(python3 --version)"
    else
        echo "  ❌ 未安装Python3"
    fi
    
    if command -v python &> /dev/null; then
        echo "  ✅ Python2: $(python --version 2>&1)"
    else
        echo "  ❌ 未安装Python2"
    fi
    
    if command -v node &> /dev/null; then
        echo "  ✅ Node.js: $(node --version)"
    else
        echo "  ❌ 未安装Node.js"
    fi
    
    if command -v php &> /dev/null; then
        echo "  ✅ PHP: $(php --version | head -1)"
    else
        echo "  ❌ 未安装PHP"
    fi
    
    # 检查SRS服务器
    echo ""
    log_info "SRS服务器状态:"
    if curl -s http://127.0.0.1:5403/api/v1/summaries > /dev/null 2>&1; then
        echo "  ✅ SRS服务器运行正常"
    else
        echo "  ❌ SRS服务器未运行或无法访问"
    fi
    
    # 检查测试文件
    echo ""
    log_info "测试文件检查:"
    if [ -f "$HTML_FILE" ]; then
        echo "  ✅ WebRTC测试页面存在"
    else
        echo "  ❌ WebRTC测试页面不存在"
    fi
    
    echo ""
    read -p "按回车键返回主菜单..."
}

# 显示详细解决方案
show_detailed_solutions() {
    log_step "详细解决方案说明"
    echo ""
    
    echo -e "${BLUE}=== 方案1: 本地HTTP服务器 (推荐) ===${NC}"
    echo "优点: 安全、简单、跨平台"
    echo "步骤:"
    echo "  1. 选择菜单选项1启动HTTP服务器"
    echo "  2. 在浏览器中访问 http://localhost:8080/web/webrtc-test.html"
    echo "  3. 正常使用WebRTC功能"
    echo ""
    
    echo -e "${BLUE}=== 方案2: Chrome开发模式 ===${NC}"
    echo "优点: 无需额外服务器"
    echo "缺点: 降低安全性，仅适用于开发"
    echo "步骤:"
    echo "  1. 关闭所有Chrome窗口"
    echo "  2. 使用特殊参数启动Chrome"
    echo "  3. 直接打开file://协议的HTML文件"
    echo ""
    
    echo -e "${BLUE}=== 方案3: HTTPS部署 (生产环境) ===${NC}"
    echo "优点: 最安全、生产就绪"
    echo "缺点: 需要SSL证书配置"
    echo "步骤:"
    echo "  1. 获取SSL证书"
    echo "  2. 配置HTTPS服务器"
    echo "  3. 通过HTTPS访问页面"
    echo ""
    
    echo -e "${BLUE}=== 方案4: localhost访问 ===${NC}"
    echo "优点: 简单、安全"
    echo "缺点: 需要服务器运行在本地"
    echo "步骤:"
    echo "  1. 在本地启动HTTP服务器"
    echo "  2. 通过localhost访问"
    echo ""
    
    echo -e "${YELLOW}注意事项:${NC}"
    echo "  - 生产环境必须使用HTTPS"
    echo "  - 开发模式仅用于测试"
    echo "  - 确保SRS服务器正常运行"
    echo "  - 检查防火墙和网络配置"
    echo ""
    
    read -p "按回车键返回主菜单..."
}

# 主函数
main() {
    while true; do
        clear
        show_menu
        read -r choice
        
        case $choice in
            1)
                clear
                start_http_server
                ;;
            2)
                clear
                generate_chrome_command
                ;;
            3)
                clear
                check_environment
                ;;
            4)
                clear
                show_detailed_solutions
                ;;
            5)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_error "无效选项，请重新选择"
                sleep 1
                ;;
        esac
    done
}

# 执行主函数
main "$@"
